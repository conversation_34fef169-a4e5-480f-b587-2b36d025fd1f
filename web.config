<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <system.webServer>
        <!-- Configure iisnode to handle Node.js applications -->
        <handlers>
            <!-- Handle all requests to the Node.js application -->
            <add name="iisnode" path="index.js" verb="*" modules="iisnode" />
        </handlers>

        <!-- URL rewriting rules to route requests to the Node.js app -->
        <rewrite>
            <rules>
                <!-- Route API and WebSocket requests to the Node.js server -->
                <rule name="NodeAPI" stopProcessing="true">
                    <match url="^(api|socket\.io).*" />
                    <action type="Rewrite" url="index.js"/>
                </rule>

                <!-- Route debug requests to Node.js application -->
                <rule name="NodeInspector" patternSyntax="ECMAScript" stopProcessing="true">
                    <match url="^index.js\/debug[\/]?" />
                </rule>

                <!-- Serve static files directly -->
                <rule name="StaticFiles" stopProcessing="true">
                    <match url="^(assets|CardFaces|SuitFaces|.*\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)).*" />
                    <action type="None" />
                </rule>

                <!-- Route all other dynamic requests to the Node.js application -->
                <rule name="DynamicContent">
                    <conditions>
                        <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="True"/>
                        <add input="{REQUEST_URI}" pattern="^/(assets|CardFaces|SuitFaces)" negate="True"/>
                    </conditions>
                    <action type="Rewrite" url="index.js"/>
                </rule>
            </rules>
        </rewrite>

        <!-- Configure iisnode settings -->
        <iisnode
            nodeProcessCommandLine="&quot;%programfiles%\nodejs\node.exe&quot;"
            interceptor="&quot;%programfiles%\iisnode\interceptor.js&quot;"
            enableXFF="true"
            promoteServerVars="LOGON_USER,AUTH_USER,AUTH_TYPE"
            debuggingEnabled="false"
            loggingEnabled="true"
            logDirectory="iisnode"
            maxNamedPipeConnectionRetry="3"
            namedPipeConnectionRetryDelay="2000"
            maxNamedPipeConnectionPoolSize="512"
            maxNamedPipePooledConnectionAge="30000"
            asyncCompletionThreadCount="0"
            initialRequestBufferSize="4096"
            maxRequestBufferSize="65536"
            watchedFiles="*.js"
            uncFileChangesPollingInterval="5000"
            gracefulShutdownTimeout="60000"
            recycleSignalEnabled="false"
            idlePageOutTimePeriod="0"
            readResponseHeaders="false"
            enableLogging="true" />

        <!-- Security settings -->
        <security>
            <requestFiltering>
                <hiddenSegments>
                    <add segment="node_modules" />
                    <add segment="iisnode" />
                </hiddenSegments>
            </requestFiltering>
        </security>

        <!-- Default document -->
        <defaultDocument>
            <files>
                <add value="index.js" />
            </files>
        </defaultDocument>

        <!-- Static content settings -->
        <staticContent>
            <mimeMap fileExtension=".json" mimeType="application/json" />
        </staticContent>
    </system.webServer>
</configuration>