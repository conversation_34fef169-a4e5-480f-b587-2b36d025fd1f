/**
 * Utility functions for handling play timeframe voting
 */

/**
 * Initialize timeframe voting state for a lobby
 * @param {Object} lobby - The lobby object
 */
function initTimeframeVoting(lobby) {
  // Initialize timeframe voting state if it doesn't exist
  if (!lobby.timeframeVoting) {
    console.log(`Initializing timeframe voting for lobby ${lobby.lobbyCode}`);

    lobby.timeframeVoting = {
      votes: {}, // Map of player ID to their vote
      results: {}, // Dynamic results based on votes
      selectedTimeframe: 3, // Default timeframe
      votingComplete: false
    };

    // Log the player count in the lobby
    const team1Count = lobby.teams[1]?.length || 0;
    const team2Count = lobby.teams[2]?.length || 0;
    const totalPlayers = team1Count + team2Count;

    console.log(`Lobby ${lobby.lobbyCode} has ${totalPlayers} players (${team1Count} in team 1, ${team2Count} in team 2)`);
  }

  return lobby.timeframeVoting;
}

/**
 * Record a player's vote for a timeframe
 * @param {Object} lobby - The lobby object
 * @param {String} playerId - The ID of the player voting
 * @param {Number} timeframe - The timeframe the player voted for (3, 4, 5, or 6 seconds)
 * @returns {Object} The updated voting state
 */
function recordTimeframeVote(lobby, playerId, timeframe) {
  // Initialize timeframe voting if needed
  initTimeframeVoting(lobby);

  // Validate the timeframe - accept any reasonable positive integer
  if (!Number.isInteger(timeframe) || timeframe < 1 || timeframe > 3600) {
    throw new Error(`Invalid timeframe: ${timeframe}. Must be a positive integer between 1 and 3600 seconds (1 hour).`);
  }

  console.log(`Recording vote from player ${playerId}: ${timeframe} seconds`);

  // Record the vote
  lobby.timeframeVoting.votes[playerId] = timeframe;

  // Update the results
  updateVoteResults(lobby);

  // Log the current voting state
  const team1Count = lobby.teams[1]?.length || 0;
  const team2Count = lobby.teams[2]?.length || 0;
  const playerCount = team1Count + team2Count;
  const voteCount = Object.keys(lobby.timeframeVoting.votes).length;

  console.log(`Current voting state: ${voteCount}/${playerCount} votes recorded`);
  console.log(`Vote results:`, lobby.timeframeVoting.results);

  return lobby.timeframeVoting;
}

/**
 * Update the vote results based on the current votes
 * @param {Object} lobby - The lobby object
 */
function updateVoteResults(lobby) {
  // Reset the results - initialize with 0 for all voted timeframes
  lobby.timeframeVoting.results = {};

  // Count the votes and initialize results dynamically
  for (const playerId in lobby.timeframeVoting.votes) {
    const vote = lobby.timeframeVoting.votes[playerId];
    if (!lobby.timeframeVoting.results[vote]) {
      lobby.timeframeVoting.results[vote] = 0;
    }
    lobby.timeframeVoting.results[vote]++;
  }
}

/**
 * Check if all players have voted
 * @param {Object} lobby - The lobby object
 * @returns {Boolean} True if all players have voted, false otherwise
 */
function allPlayersVoted(lobby) {
  // Get the total number of players from both teams
  const team1Count = lobby.teams[1]?.length || 0;
  const team2Count = lobby.teams[2]?.length || 0;
  const playerCount = team1Count + team2Count;

  // Get the number of votes
  const voteCount = Object.keys(lobby.timeframeVoting.votes).length;

  console.log(`Checking if all players have voted: ${voteCount} votes out of ${playerCount} players`);

  return voteCount === playerCount && playerCount === 4;
}

/**
 * Determine the selected timeframe based on the votes
 * @param {Object} lobby - The lobby object
 * @returns {Number} The selected timeframe
 */
function determineSelectedTimeframe(lobby) {
  // Get the results
  const results = lobby.timeframeVoting.results;

  // Find the timeframe with the most votes
  let maxVotes = 0;
  let selectedTimeframe = 3; // Default to 3 seconds
  const timeframes = Object.keys(results);

  for (const timeframe in results) {
    if (results[timeframe] > maxVotes) {
      maxVotes = results[timeframe];
      selectedTimeframe = parseInt(timeframe);
    }
  }

  // If there's a tie, use the smallest timeframe value
  const tiedTimeframes = Object.keys(results).filter(t => results[t] === maxVotes);
  if (tiedTimeframes.length > 1) {
    selectedTimeframe = Math.min(...tiedTimeframes.map(t => parseInt(t)));
  }

  // If no votes were cast, use the first available timeframe or default to 3
  if (timeframes.length === 0 || maxVotes === 0) {
    selectedTimeframe = 3;
  }

  // Update the selected timeframe
  lobby.timeframeVoting.selectedTimeframe = selectedTimeframe;
  lobby.timeframeVoting.votingComplete = true;

  return selectedTimeframe;
}

/**
 * Get the current voting state
 * @param {Object} lobby - The lobby object
 * @returns {Object} The current voting state
 */
function getVotingState(lobby) {
  // Initialize timeframe voting if needed
  initTimeframeVoting(lobby);

  return {
    votes: lobby.timeframeVoting.votes,
    results: lobby.timeframeVoting.results,
    selectedTimeframe: lobby.timeframeVoting.selectedTimeframe,
    votingComplete: lobby.timeframeVoting.votingComplete
  };
}

/**
 * Reset the timeframe voting state
 * @param {Object} lobby - The lobby object
 */
function resetTimeframeVoting(lobby) {
  lobby.timeframeVoting = {
    votes: {},
    results: {}, // Dynamic results based on votes
    selectedTimeframe: 3,
    votingComplete: false
  };
}

/**
 * Initialize turn timer for a player
 * @param {Object} lobby - The lobby object
 * @param {String} playerId - The ID of the player whose turn it is
 */
function initTurnTimer(lobby, playerId) {
  // Clear any existing timer
  if (lobby.turnTimer) {
    clearTimeout(lobby.turnTimer);
    lobby.turnTimer = null;
  }

  // Check if this is the first player after Thunee opportunities
  const isFirstPlayerAfterThunee = lobby.thuneeOpportunitiesComplete && lobby.firstPlayerId === playerId;

  // Initialize turn timer state
  lobby.turnTimerState = {
    playerId,
    startTime: Date.now(),
    timeRemaining: lobby.timeframeVoting?.selectedTimeframe || 3, // Default to 3 seconds if not set
    timerActive: true,
    isFirstPlayerAfterThunee
  };

  // Return the initial state
  return lobby.turnTimerState;
}

/**
 * Update the turn timer
 * @param {Object} lobby - The lobby object
 * @returns {Object} The updated turn timer state
 */
function updateTurnTimer(lobby) {
  if (!lobby.turnTimerState || !lobby.turnTimerState.timerActive) {
    return null;
  }

  const { startTime, timeRemaining } = lobby.turnTimerState;
  const elapsed = (Date.now() - startTime) / 1000;
  const remaining = Math.max(0, timeRemaining - elapsed);

  // Update the time remaining
  lobby.turnTimerState.currentRemaining = remaining;

  return {
    ...lobby.turnTimerState,
    currentRemaining: remaining
  };
}

/**
 * Stop the turn timer
 * @param {Object} lobby - The lobby object
 */
function stopTurnTimer(lobby) {
  if (lobby.turnTimer) {
    clearTimeout(lobby.turnTimer);
    lobby.turnTimer = null;
  }

  if (lobby.turnTimerState) {
    lobby.turnTimerState.timerActive = false;
  }
}

export default {
  initTimeframeVoting,
  recordTimeframeVote,
  updateVoteResults,
  allPlayersVoted,
  determineSelectedTimeframe,
  getVotingState,
  resetTimeframeVoting,
  initTurnTimer,
  updateTurnTimer,
  stopTurnTimer
};
