import express from 'express';
import http from 'http';
import { Server } from 'socket.io';
import cors from 'cors';
import { v4 as uuidv4 } from 'uuid';
import { handleSetDealer } from './src/handlers/dealerHandler.js';
import { handleJordhiReveal } from './src/handlers/jordhiRevealHandler.js';
import doubleHandler from './src/handlers/doubleHandler.js';
import khanakHandler from './src/handlers/khanakHandler.js';
import followSuitHandler from './src/handlers/followSuitHandler.js';
import fourBallHandler from './src/handlers/fourBallHandler.js';
import underChoppedHandler from './src/handlers/underChoppedHandler.js';
import ballUtils from './src/utils/server/ballUtils.js';
import timeframeUtils from './src/utils/server/timeframeUtils.js';
import turnUtils from './src/utils/server/turnUtils.js';
import gameEndUtils from './src/utils/server/gameEndUtils.js';
import cardUtils from './src/utils/server/cardUtils.js';
import dealingUtils from './src/utils/server/dealingUtils.js';
import finalCardDealer from './src/utils/server/finalCardDealer.js';
import gameUtils from './src/utils/server/gameUtils.js';
import playerPositionUtils from './src/utils/server/playerPositionUtils.js';
import resetUtils from './src/utils/server/resetUtils.js';
import spectatorUtils from './src/utils/server/spectatorUtils.js';

const app = express();
app.use(cors());

// Create a variable to store the port
let currentPort = 0;

// Add a simple route to serve a test page
app.get('/', (req, res) => {
  res.send(`
    <html>
      <head>
        <title>Thunee WebSocket Server</title>
        <style>
          body { font-family: Arial, sans-serif; background: #222; color: #E1C760; margin: 0; padding: 20px; text-align: center; }
          .container { max-width: 800px; margin: 0 auto; background: #333; padding: 20px; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.3); }
          h1 { color: #E1C760; }
          .status { padding: 10px; background: #444; border-radius: 4px; margin: 20px 0; }
          .success { color: #4CAF50; }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>Thunee WebSocket Server</h1>
          <div class="status">
            <p>Server is running on port <strong>${currentPort}</strong></p>
            <p class="success">✓ WebSocket server is active</p>
          </div>
          <p>This server handles WebSocket connections for the Thunee card game.</p>
          <p>To play the game, please open the Thunee game client application.</p>
          <p><a href="/debug/match-queue" style="color: #E1C760;">View Match Queue Debug Info</a></p>
        </div>
      </body>
    </html>
  `);
});

// Debug endpoint to check match queue status
app.get('/debug/match-queue', (req, res) => {
  const queueInfo = Array.from(matchQueue).map(lobbyCode => {
    const lobby = lobbies.get(lobbyCode);
    return {
      lobbyCode,
      exists: !!lobby,
      isFindingMatch: lobby ? lobby.isFindingMatch : false,
      teamReady: lobby ? lobby.teamReady : {},
      team1Size: lobby ? lobby.teams[1].length : 0,
      matchedLobby: lobby ? lobby.matchedLobby : null
    };
  });

  res.json({
    queueSize: matchQueue.size,
    queue: queueInfo,
    totalLobbies: lobbies.size
  });
});

// Debug endpoint to force a match between two lobbies
app.get('/debug/force-match', (req, res) => {
  const { lobby1, lobby2 } = req.query;

  if (!lobby1 || !lobby2) {
    return res.json({ success: false, error: 'Both lobby1 and lobby2 parameters are required' });
  }

  const lobby1Obj = lobbies.get(lobby1);
  const lobby2Obj = lobbies.get(lobby2);

  if (!lobby1Obj || !lobby2Obj) {
    return res.json({ success: false, error: 'One or both lobbies not found' });
  }

  // Force match between the two lobbies
  console.log(`Forcing match between ${lobby1} and ${lobby2}`);

  // Update both lobbies
  lobby1Obj.isFindingMatch = false;
  lobby1Obj.matchedLobby = lobby2;
  lobby2Obj.isFindingMatch = false;
  lobby2Obj.matchedLobby = lobby1;

  // Remove from queue if they're in it
  matchQueue.delete(lobby1);
  matchQueue.delete(lobby2);

  // Notify both lobbies
  io.to(lobby1).emit('match_found', {
    matchedLobby: lobby2,
    matchedTeam: lobby2Obj.teams[1],
    matchedTeamName: lobby2Obj.teamNames[1]
  });

  io.to(lobby2).emit('match_found', {
    matchedLobby: lobby1,
    matchedTeam: lobby1Obj.teams[1],
    matchedTeamName: lobby1Obj.teamNames[1]
  });

  // Also send match_status_update for more reliable state updates
  io.to(lobby1).emit('match_status_update', {
    isFindingMatch: false,
    matchedLobby: lobby2,
    matchedTeam: lobby2Obj.teams[1],
    matchedTeamName: lobby2Obj.teamNames[1]
  });

  io.to(lobby2).emit('match_status_update', {
    isFindingMatch: false,
    matchedLobby: lobby1,
    matchedTeam: lobby1Obj.teams[1],
    matchedTeamName: lobby1Obj.teamNames[1]
  });

  return res.json({ success: true, message: 'Match forced successfully' });
});

const server = http.createServer(app);
const io = new Server(server, {
  cors: {
    origin: '*', // In production, restrict this to your frontend URL
    methods: ['GET', 'POST']
  },
  transports: ['websocket', 'polling'],
  pingTimeout: 60000,  // Increased timeout
  pingInterval: 15000, // More frequent pings
  connectTimeout: 30000, // Longer connect timeout
  maxHttpBufferSize: 1e8 // Increased buffer size for larger payloads
});

// Store active lobbies and their players
const lobbies = new Map();
// Store socket ID to lobby code mapping
const socketToLobby = new Map();
// Store lobbies that are looking for a match
const matchQueue = new Set();
// Store game lobbies (merged lobbies after matching)
const gameLobbies = new Map();
// Store socket ID to game lobby code mapping
const socketToGameLobby = new Map();
// Store spectator socket ID to game lobby code mapping
const spectatorToGameLobby = new Map();

// Helper functions
function getRandomInt(max) {
  return Math.floor(Math.random() * max);
}

// Helper function to get the team of the initial trumper (player to the right of dealer)
function getInitialTrumperTeam(lobby, matchedLobby) {
  // Get all players from both lobbies
  const allPlayers = [];
  if (lobby.players && Array.isArray(lobby.players)) {
    allPlayers.push(...lobby.players);
  }
  if (matchedLobby.players && Array.isArray(matchedLobby.players)) {
    allPlayers.push(...matchedLobby.players);
  }

  // If we don't have a dealer, we can't determine the initial trumper
  if (!lobby.dealerId) {
    return null;
  }

  // Find the dealer
  const dealer = allPlayers.find(p => p.id === lobby.dealerId);
  if (!dealer) {
    return null;
  }

  // If the dealer has a position assigned, use it to determine the initial trumper
  if (dealer.position) {
    // The initial trumper is always at position 1 (to the right of dealer at position 3)
    const initialTrumperPosition = playerPositionUtils.getInitialTrumperPosition();
    const initialTrumper = allPlayers.find(p => p.position === initialTrumperPosition);

    if (initialTrumper) {
      return initialTrumper.team;
    }
  }

  // Fallback to the old method if positions are not assigned
  // Find the dealer's index in the player array
  const dealerIndex = allPlayers.findIndex(p => p.id === lobby.dealerId);
  if (dealerIndex === -1) {
    return null;
  }

  // The initial trumper is the player to the right of the dealer
  // In a 4-player game with counter-clockwise play, this is the player at index (dealerIndex + 3) % 4
  const initialTrumperIndex = (dealerIndex + 3) % allPlayers.length;
  const initialTrumper = allPlayers[initialTrumperIndex];

  if (!initialTrumper) {
    return null;
  }

  return initialTrumper.team;
}

// Function to create a unified game lobby from two matched lobbies
function createGameLobby(lobby1Code, lobby2Code) {
  const lobby1 = lobbies.get(lobby1Code);
  const lobby2 = lobbies.get(lobby2Code);

  if (!lobby1 || !lobby2) {
    console.error('Cannot create game lobby: one or both lobbies not found');
    return null;
  }

  // Generate a unique game lobby code
  const gameLobbyCode = `GAME-${generateLobbyCode()}`;

  // Combine players from both lobbies
  const allPlayers = [];

  // Add players from both teams in both lobbies
  if (lobby1.teams && lobby2.teams) {
    for (const teamId of [1, 2]) {
      if (lobby1.teams[teamId]) {
        allPlayers.push(...lobby1.teams[teamId]);
      }
      if (lobby2.teams[teamId]) {
        allPlayers.push(...lobby2.teams[teamId]);
      }
    }
  } else {
    // Fallback to players array
    if (Array.isArray(lobby1.players)) {
      allPlayers.push(...lobby1.players);
    }
    if (Array.isArray(lobby2.players)) {
      allPlayers.push(...lobby2.players);
    }
  }

  // Create the game lobby object
  const gameLobby = {
    lobbyCode: gameLobbyCode,
    players: allPlayers,
    originalLobbies: [lobby1Code, lobby2Code],
    teams: {
      1: allPlayers.filter(p => p.team === 1),
      2: allPlayers.filter(p => p.team === 2)
    },
    teamNames: {
      1: lobby1.teamNames?.[1] || 'Team 1',
      2: lobby2.teamNames?.[1] || 'Team 2'
    },
    gameStarted: true,
    gameState: {
      ...lobby1.gameState,
      ...lobby2.gameState
    },
    dealerId: lobby1.dealerId || lobby2.dealerId,
    trumpSelectorId: lobby1.trumpSelectorId || lobby2.trumpSelectorId,
    trumpSuit: lobby1.trumpSuit || lobby2.trumpSuit,
    playerCards: { ...lobby1.playerCards, ...lobby2.playerCards }
  };

  // Store the game lobby
  gameLobbies.set(gameLobbyCode, gameLobby);

  // Update socket to game lobby mapping for all players
  allPlayers.forEach(player => {
    socketToGameLobby.set(player.id, gameLobbyCode);

    // Make the player join the game lobby room
    const socket = io.sockets.sockets.get(player.id);
    if (socket) {
      socket.join(gameLobbyCode);
    }
  });

  console.log(`Created game lobby ${gameLobbyCode} with ${allPlayers.length} players`);
  return gameLobby;
}

// Create a shuffled deck for dealer determination
function createShuffledDeck() {
  // For dealer determination, we only need the Jack of Clubs and Jack of Spades
  // But we'll create a full deck and shuffle it
  const suits = ['hearts', 'diamonds', 'clubs', 'spades'];
  const values = ['9', '10', 'J', 'Q', 'K', 'A'];

  let deck = [];

  for (const suit of suits) {
    for (const value of values) {
      deck.push({ suit, value });
    }
  }

  // Shuffle the deck
  for (let i = deck.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [deck[i], deck[j]] = [deck[j], deck[i]];
  }

  // Ensure the black jacks (Jack of Clubs and Jack of Spades) are not at the very end
  // This prevents the dealer determination from taking too long
  const blackJackIndices = deck.reduce((indices, card, index) => {
    if (card.value === 'J' && (card.suit === 'clubs' || card.suit === 'spades')) {
      indices.push(index);
    }
    return indices;
  }, []);

  // If both black jacks are in the last quarter of the deck, move one to the first half
  if (blackJackIndices.length === 2 &&
      blackJackIndices[0] > deck.length * 0.75 &&
      blackJackIndices[1] > deck.length * 0.75) {
    // Swap one black jack with a random card in the first half
    const randomFirstHalfIndex = Math.floor(Math.random() * (deck.length / 2));
    [deck[blackJackIndices[0]], deck[randomFirstHalfIndex]] =
    [deck[randomFirstHalfIndex], deck[blackJackIndices[0]]];
  }

  return deck;
}



// Create a game deck for Thunee (24 cards)
function createGameDeck() {
  return cardUtils.createGameDeck();
}

// Function to start the game after dealer determination
function startGame(lobby, matchedLobby, allPlayers, dealerId) {
  try {
    console.log(`Starting game with dealer ${dealerId}`);

    // Reset game state for a fresh start
    resetUtils.resetGameState(lobby, matchedLobby);

    // Initialize game history tracking
    gameEndUtils.initializeGameHistory(lobby);
    if (matchedLobby !== lobby) {
      gameEndUtils.initializeGameHistory(matchedLobby);
    }

    // Mark both lobbies as started
    lobby.gameStarted = true;
    matchedLobby.gameStarted = true;

    // Set the dealer in both lobbies
    lobby.dealerId = dealerId;
    matchedLobby.dealerId = dealerId;

    // Register the game in the gameLobbies map for spectator access
    // Use the original lobby code as the game lobby code
    gameLobbies.set(lobby.lobbyCode, {
      ...lobby,
      spectators: [],
      originalLobbies: [lobby.lobbyCode, matchedLobby.lobbyCode]
    });

    console.log(`Registered game lobby ${lobby.lobbyCode} for spectator access`);
    console.log(`Available game lobbies: ${Array.from(gameLobbies.keys()).join(', ')}`);

    // Find the dealer player
    const dealer = allPlayers.find(p => p.id === dealerId);
    if (!dealer) {
      console.error(`Dealer with ID ${dealerId} not found in players list`);
      return;
    }

    // Mark the dealer in the player list
    dealer.isDealer = true;

    // playerPositionUtils is already imported at the top

    // Assign fixed positions to players based on the dealer
    // This is crucial for determining the initial trumper correctly
    const positionedPlayers = playerPositionUtils.assignPositionsBasedOnDealer(allPlayers, dealerId);

    // Log the positioned players for debugging
    console.log('Assigned positions to players:',
      positionedPlayers.map(p => `${p.name} (${p.id}) - Team ${p.team} - Position ${p.position}`)
    );

    // Update the players in both lobbies with their assigned positions
    lobby.players = positionedPlayers.filter(p => lobby.players.some(lp => lp.id === p.id));
    matchedLobby.players = positionedPlayers.filter(p => matchedLobby.players.some(mp => mp.id === p.id));

    // Also update the teams in both lobbies
    lobby.teams[1] = positionedPlayers.filter(p => p.team === 1 && lobby.players.some(lp => lp.id === p.id));
    lobby.teams[2] = positionedPlayers.filter(p => p.team === 2 && lobby.players.some(lp => lp.id === p.id));
    matchedLobby.teams[1] = positionedPlayers.filter(p => p.team === 1 && matchedLobby.players.some(mp => mp.id === p.id));
    matchedLobby.teams[2] = positionedPlayers.filter(p => p.team === 2 && matchedLobby.players.some(mp => mp.id === p.id));

    // Notify all players in both lobbies that the game is moving to shuffle phase
    // Include the updated player positions in the notification
    io.to(lobby.lobbyCode).emit('game_phase_updated', {
      phase: 'shuffle',
      players: positionedPlayers
    });
    io.to(matchedLobby.lobbyCode).emit('game_phase_updated', {
      phase: 'shuffle',
      players: positionedPlayers
    });

    console.log(`Game phase updated to shuffle for lobbies ${lobby.lobbyCode} and ${matchedLobby.lobbyCode}`);

    console.log(`Game started between lobbies ${lobby.lobbyCode} and ${matchedLobby.lobbyCode} with dealer ${dealer.name}`);
  } catch (error) {
    console.error('Error starting game after dealer determination:', error);
  }
}

// Deal the next card in the dealer determination process
function dealNextCard(lobby, matchedLobby, allPlayers) {
  // If dealer already found, do nothing
  if (lobby.dealerFound) {
    // Reset dealer determination in progress flag
    lobby.dealerDeterminationInProgress = false;
    matchedLobby.dealerDeterminationInProgress = false;
    return;
  }

  // For dealer determination, we need a fresh deck of cards
  // Create a Thunee deck (24 cards: 9, 10, J, Q, K, A from each suit)
  if (!lobby.dealerDeck || lobby.dealerDeck.length === 0) {
    console.log('Creating a new deck for dealer determination');
    const suits = ['hearts', 'diamonds', 'clubs', 'spades'];
    const values = ['9', '10', 'J', 'Q', 'K', 'A'];

    let deck = [];
    for (const suit of suits) {
      for (const value of values) {
        deck.push({ suit, value, id: `${value}_${suit}_${Math.random()}` });
      }
    }

    // Shuffle the deck
    for (let i = deck.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [deck[i], deck[j]] = [deck[j], deck[i]];
    }

    lobby.dealerDeck = deck;
    if (matchedLobby !== lobby) {
      matchedLobby.dealerDeck = [...deck]; // Create a copy for the matched lobby
    }

    console.log(`Created dealer determination deck with ${deck.length} cards`);

    // Reset cards dealt counter
    lobby.cardsDealtPerPlayer = {};
    if (matchedLobby !== lobby) {
      matchedLobby.cardsDealtPerPlayer = {};
    }

    allPlayers.forEach(player => {
      lobby.cardsDealtPerPlayer[player.id] = 0;
      if (matchedLobby !== lobby) {
        matchedLobby.cardsDealtPerPlayer[player.id] = 0;
      }
    });
  }

  // Get the current player
  let currentPlayerIndex = lobby.currentDealerPlayerIndex;
  let currentPlayer = allPlayers[currentPlayerIndex];

  // Check if this player already has too many cards (safety check)
  if (lobby.cardsDealtPerPlayer[currentPlayer.id] >= 24) {
    console.log(`Player ${currentPlayer.name} already has too many cards, moving to next player`);
    // Move to the next player
    lobby.currentDealerPlayerIndex = (currentPlayerIndex + 1) % allPlayers.length;
    if (matchedLobby !== lobby) {
      matchedLobby.currentDealerPlayerIndex = lobby.currentDealerPlayerIndex;
    }

    // Schedule the next card deal
    setTimeout(() => {
      dealNextCard(lobby, matchedLobby, allPlayers);
    }, 500);
    return;
  }

  // Verify all players are still connected before proceeding
  const connectedSockets = io.sockets.adapter.rooms.get(lobby.lobbyCode);

  // For invite code case, we only need to check one lobby
  const isSingleLobby = (lobby === matchedLobby);

  if (!connectedSockets ||
      (!isSingleLobby && !io.sockets.adapter.rooms.get(matchedLobby.lobbyCode))) {
    console.error('Not all players are connected during dealNextCard, aborting dealer determination');
    lobby.dealerDeterminationInProgress = false;
    matchedLobby.dealerDeterminationInProgress = false;

    // Notify all clients that dealer determination has been aborted
    io.to(lobby.lobbyCode).emit('dealer_determination_aborted', { reason: 'Not all players are connected' });
    if (!isSingleLobby) {
      io.to(matchedLobby.lobbyCode).emit('dealer_determination_aborted', { reason: 'Not all players are connected' });
    }
    return;
  }

  // Get the current player index (already declared above)
  // Update the current player index from the lobby
  currentPlayerIndex = lobby.currentDealerPlayerIndex;

  // Ensure the player exists in the array
  if (currentPlayerIndex >= allPlayers.length) {
    console.error('Invalid player index:', currentPlayerIndex, 'allPlayers length:', allPlayers.length);
    return;
  }

  // Ensure we have exactly 4 players
  if (allPlayers.length !== 4) {
    console.error('Expected 4 players but got', allPlayers.length, 'in dealNextCard');
    return;
  }

  // Update the current player reference
  currentPlayer = allPlayers[currentPlayerIndex];
  // console.log(`Dealing card to player ${currentPlayer.name} (${currentPlayer.id}) - Team ${currentPlayer.team}`);

  // First, notify all clients that we're about to deal a card to this player
  // Include more information about the player to ensure all clients can identify them
  const dealingToInfo = {
    playerId: currentPlayer.id,
    playerName: currentPlayer.name,
    playerTeam: currentPlayer.team,
    playerIndex: currentPlayerIndex
  };

  // Send to both lobbies to ensure all players see the same information
  io.to(lobby.lobbyCode).emit('dealing_card_to', dealingToInfo);
  io.to(matchedLobby.lobbyCode).emit('dealing_card_to', dealingToInfo);

  // After a short delay, deal the actual card
  setTimeout(() => {
    // Get the next card from the deck
    const card = lobby.dealerDeck.pop();

    // Check if this is a black Jack (Jack of Clubs or Jack of Spades)
    const isBlackJack = card.value === 'J' && (card.suit === 'clubs' || card.suit === 'spades');

    // Broadcast the card dealt to all players with enhanced information
    const dealInfo = {
      playerId: currentPlayer.id,
      playerName: currentPlayer.name,
      playerTeam: currentPlayer.team,
      playerIndex: currentPlayerIndex,
      card: card,
      isDealer: isBlackJack,
      cardsDealtToPlayer: lobby.cardsDealtPerPlayer[currentPlayer.id] + 1, // +1 because we increment after this
      totalCardsDealt: Object.values(lobby.cardsDealtPerPlayer).reduce((sum, count) => sum + count, 0) + 1 // +1 for current card
    };

    // Send to all players - this will display the card to all players
    io.to(lobby.lobbyCode).emit('card_dealt', dealInfo);
    if (!isSingleLobby) {
      io.to(matchedLobby.lobbyCode).emit('card_dealt', dealInfo);
    }

    // Increment the card count for this player
    lobby.cardsDealtPerPlayer[currentPlayer.id]++;
    if (!isSingleLobby) {
      matchedLobby.cardsDealtPerPlayer[currentPlayer.id]++;
    }

  // If this is a black Jack, we found our dealer
  if (isBlackJack) {
    lobby.dealerFound = true;
    matchedLobby.dealerFound = true;
    lobby.dealerId = currentPlayer.id;
    matchedLobby.dealerId = currentPlayer.id;

    // Determine the trump selector (player from opposite team)
    const team = currentPlayer.team;
    const oppositeTeam = team === 1 ? 2 : 1;
    const oppositeTeamPlayers = allPlayers.filter(p => p.team === oppositeTeam);

    // If no opposite team players found, use any other player
    let trumpSelector;
    if (oppositeTeamPlayers.length > 0) {
      // In Thunee, the trump selector should be from the opposite team
      // Ideally, it should be the player sitting across from the dealer
      // For simplicity, we'll just pick a random player from the opposite team
      const trumpSelectorIndex = getRandomInt(oppositeTeamPlayers.length);
      trumpSelector = oppositeTeamPlayers[trumpSelectorIndex];
    } else {
      // Fallback: use any player that's not the dealer
      const otherPlayers = allPlayers.filter(p => p.id !== currentPlayer.id);
      if (otherPlayers.length > 0) {
        const trumpSelectorIndex = getRandomInt(otherPlayers.length);
        trumpSelector = otherPlayers[trumpSelectorIndex];
      } else {
        // Extreme fallback: use the dealer if no other players
        trumpSelector = currentPlayer;
      }
    }

    console.log(`Dealer: ${currentPlayer.name} (Team ${currentPlayer.team}), Trump Selector: ${trumpSelector.name} (Team ${trumpSelector.team})`);

    // Broadcast dealer found to all players with enhanced information
    const dealerInfo = {
      dealerId: currentPlayer.id,
      dealerName: currentPlayer.name,
      dealerTeam: currentPlayer.team,
      trumpSelectorId: trumpSelector.id,
      trumpSelectorName: trumpSelector.name,
      trumpSelectorTeam: trumpSelector.team,
      dealerCard: card, // Include the card that determined the dealer
      players: allPlayers.map(p => ({
        id: p.id,
        name: p.name,
        team: p.team,
        isDealer: p.id === currentPlayer.id,
        isTrumpSelector: p.id === trumpSelector.id
      }))
    };

    // Wait a bit longer to show the card before announcing dealer
    setTimeout(() => {
      // Reset dealer determination in progress flag
      lobby.dealerDeterminationInProgress = false;
      matchedLobby.dealerDeterminationInProgress = false;

      // Broadcast dealer found to all players
      io.to(lobby.lobbyCode).emit('dealer_found', dealerInfo);
      if (!isSingleLobby) {
        io.to(matchedLobby.lobbyCode).emit('dealer_found', dealerInfo);
      }

      // After a delay, start the game
      setTimeout(() => {
        startGame(lobby, matchedLobby, allPlayers, currentPlayer.id);
      }, 3000); // 3 second delay
    }, 2000);

    return;
  }

  // Move to the next player in clockwise order
  // In a 4-player game, clockwise means: 0 -> 1 -> 2 -> 3 -> 0
  lobby.currentDealerPlayerIndex = (currentPlayerIndex + 1) % allPlayers.length;
  if (!isSingleLobby) {
    matchedLobby.currentDealerPlayerIndex = lobby.currentDealerPlayerIndex;
  }

  // Schedule the next card deal after a delay
  setTimeout(() => {
    dealNextCard(lobby, matchedLobby, allPlayers);
  }, 1500); // 1.5 second delay between deals
  }, 500); // 0.5 second delay before dealing the card
}

// Function to check for matches periodically
function checkForMatches() {
  // console.log('Running periodic match check...');
  if (matchQueue.size < 2) {
    // console.log('Not enough teams in queue for matching:', matchQueue.size);
    if (matchQueue.size > 0) {
      console.log('Teams in queue:', Array.from(matchQueue));

      // Debug each lobby in the queue
      Array.from(matchQueue).forEach(lobbyCode => {
        const lobby = lobbies.get(lobbyCode);
        if (lobby) {
          console.log(`Lobby ${lobbyCode} details:`, {
            isFindingMatch: lobby.isFindingMatch,
            teamReady: lobby.teamReady,
            team1Size: lobby.teams[1].length,
            team1Players: lobby.teams[1].map(p => p.name)
          });
        } else {
          console.log(`Lobby ${lobbyCode} not found in lobbies map`);
          // Clean up invalid entries
          matchQueue.delete(lobbyCode);
        }
      });
    }
    return;
  }

  console.log('Teams in match queue:', Array.from(matchQueue));

  // Convert to array for easier manipulation
  const queueArray = Array.from(matchQueue);

  // Check each lobby against others for potential matches
  for (let i = 0; i < queueArray.length; i++) {
    const lobbyCode = queueArray[i];
    const lobby = lobbies.get(lobbyCode);

    // Skip if lobby doesn't exist or is no longer looking for a match
    if (!lobby || !lobby.isFindingMatch || lobby.matchedLobby) {
      console.log(`Lobby ${lobbyCode} is no longer valid for matching`);
      matchQueue.delete(lobbyCode);
      continue;
    }

    // Look for a match
    for (let j = i + 1; j < queueArray.length; j++) {
      const otherLobbyCode = queueArray[j];
      const otherLobby = lobbies.get(otherLobbyCode);

      // Skip if other lobby doesn't exist or is no longer looking for a match
      if (!otherLobby || !otherLobby.isFindingMatch || otherLobby.matchedLobby) {
        console.log(`Lobby ${otherLobbyCode} is no longer valid for matching`);
        matchQueue.delete(otherLobbyCode);
        continue;
      }

      // Check if both lobbies are valid for matching
      if (lobby.teamReady[1] && lobby.teams[1].length === 2 &&
          otherLobby.teamReady[1] && otherLobby.teams[1].length === 2) {
        console.log(`Match found between ${lobbyCode} and ${otherLobbyCode}!`);

        // Remove both from queue
        matchQueue.delete(lobbyCode);
        matchQueue.delete(otherLobbyCode);

        // Update both lobbies
        lobby.isFindingMatch = false;
        lobby.matchedLobby = otherLobbyCode;
        otherLobby.isFindingMatch = false;
        otherLobby.matchedLobby = lobbyCode;

        // Create a unified game lobby for both teams
        const gameLobby = createGameLobby(lobbyCode, otherLobbyCode);

        if (gameLobby) {
          console.log(`Created unified game lobby: ${gameLobby.lobbyCode}`);

          // Notify both lobbies with match_found event
          io.to(lobbyCode).emit('match_found', {
            matchedLobby: otherLobbyCode,
            matchedTeam: otherLobby.teams[1],
            matchedTeamName: otherLobby.teamNames[1],
            gameLobbyCode: gameLobby.lobbyCode
          });

          io.to(otherLobbyCode).emit('match_found', {
            matchedLobby: lobbyCode,
            matchedTeam: lobby.teams[1],
            matchedTeamName: lobby.teamNames[1],
            gameLobbyCode: gameLobby.lobbyCode
          });

          // Also send match_status_update for more reliable state updates
          io.to(lobbyCode).emit('match_status_update', {
            isFindingMatch: false,
            matchedLobby: otherLobbyCode,
            matchedTeam: otherLobby.teams[1],
            matchedTeamName: otherLobby.teamNames[1],
            gameLobbyCode: gameLobby.lobbyCode
          });

          io.to(otherLobbyCode).emit('match_status_update', {
            isFindingMatch: false,
            matchedLobby: lobbyCode,
            matchedTeam: lobby.teams[1],
            matchedTeamName: lobby.teamNames[1],
            gameLobbyCode: gameLobby.lobbyCode
          });
        } else {
          // Fallback to old behavior if game lobby creation fails
          console.error('Failed to create unified game lobby, using legacy match behavior');

          // Notify both lobbies with match_found event
          io.to(lobbyCode).emit('match_found', {
            matchedLobby: otherLobbyCode,
            matchedTeam: otherLobby.teams[1],
            matchedTeamName: otherLobby.teamNames[1]
          });

          io.to(otherLobbyCode).emit('match_found', {
            matchedLobby: lobbyCode,
            matchedTeam: lobby.teams[1],
            matchedTeamName: lobby.teamNames[1]
          });

          // Also send match_status_update for more reliable state updates
          io.to(lobbyCode).emit('match_status_update', {
            isFindingMatch: false,
            matchedLobby: otherLobbyCode,
            matchedTeam: otherLobby.teams[1],
            matchedTeamName: otherLobby.teamNames[1]
          });

          io.to(otherLobbyCode).emit('match_status_update', {
            isFindingMatch: false,
            matchedLobby: lobbyCode,
            matchedTeam: lobby.teams[1],
            matchedTeamName: lobby.teamNames[1]
          });
        }

        // We found a match for this lobby, break the inner loop
        break;
      }
    }
  }
}

// Run match check every 2 seconds
setInterval(checkForMatches, 2000);

// Also run a more aggressive match check every 10 seconds
setInterval(() => {
  // console.log('Running aggressive match check...');

  // If there are at least 2 lobbies in the queue, force a match
  if (matchQueue.size >= 2) {
    const queueArray = Array.from(matchQueue);
    console.log('Attempting to force match between:', queueArray[0], 'and', queueArray[1]);

    const lobby1 = lobbies.get(queueArray[0]);
    const lobby2 = lobbies.get(queueArray[1]);

    if (lobby1 && lobby2 &&
        lobby1.isFindingMatch && lobby2.isFindingMatch &&
        lobby1.teamReady[1] && lobby2.teamReady[1] &&
        lobby1.teams[1].length === 2 && lobby2.teams[1].length === 2) {

      console.log('Force matching lobbies:', queueArray[0], 'and', queueArray[1]);

      // Remove both from queue
      matchQueue.delete(queueArray[0]);
      matchQueue.delete(queueArray[1]);

      // Update both lobbies
      lobby1.isFindingMatch = false;
      lobby1.matchedLobby = queueArray[1];
      lobby2.isFindingMatch = false;
      lobby2.matchedLobby = queueArray[0];

      // Notify both lobbies
      io.to(queueArray[0]).emit('match_found', {
        matchedLobby: queueArray[1],
        matchedTeam: lobby2.teams[1]
      });

      io.to(queueArray[1]).emit('match_found', {
        matchedLobby: queueArray[0],
        matchedTeam: lobby1.teams[1]
      });

      // Also send match_status_update for more reliable state updates
      io.to(queueArray[0]).emit('match_status_update', {
        isFindingMatch: false,
        matchedLobby: queueArray[1],
        matchedTeam: lobby2.teams[1]
      });

      io.to(queueArray[1]).emit('match_status_update', {
        isFindingMatch: false,
        matchedLobby: queueArray[0],
        matchedTeam: lobby1.teams[1]
      });
    }
  }
}, 10000);

// Generate a random 6-character lobby code
function generateLobbyCode() {
  const characters = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789';
  let result = '';
  for (let i = 0; i < 6; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return result;
}

// Get avatar URL for a player
function getAvatarUrl(name) {
  return `https://api.dicebear.com/7.x/avataaars/svg?seed=${name}`;
}

// Socket.IO connection handler
io.on('connection', (socket) => {
  console.log(`User connected: ${socket.id}`);

  // Join as a spectator
  socket.on('join_as_spectator', ({ gameCode, spectatorName }, callback) => {
    try {
      console.log(`Spectator ${spectatorName} attempting to join game ${gameCode}`);

      // Add spectator to the game
      const result = spectatorUtils.addSpectator(io, socket, gameCode, spectatorName, gameLobbies, spectatorToGameLobby, lobbies);

      if (!result.success) {
        return callback({ success: false, error: result.error });
      }

      // Send current game state to the spectator
      spectatorUtils.sendGameStateToSpectator(socket, gameCode, gameLobbies, lobbies);

      callback({
        success: true,
        gameLobby: {
          lobbyCode: result.gameLobby.lobbyCode,
          players: result.gameLobby.players,
          teams: result.gameLobby.teams,
          teamNames: result.gameLobby.teamNames,
          spectatorCount: result.gameLobby.spectators.length
        }
      });
    } catch (error) {
      console.error('Error joining as spectator:', error);
      callback({ success: false, error: 'Failed to join as spectator' });
    }
  });

  // Leave as a spectator
  socket.on('leave_spectator_mode', (data, callback) => {
    try {
      // Remove spectator from the game
      const result = spectatorUtils.removeSpectator(io, socket.id, gameLobbies, spectatorToGameLobby);

      if (!result.success) {
        return callback({ success: false, error: result.error });
      }

      callback({ success: true });
    } catch (error) {
      console.error('Error leaving spectator mode:', error);
      callback({ success: false, error: 'Failed to leave spectator mode' });
    }
  });

  // Get available games to spectate
  socket.on('get_available_games', (data, callback) => {
    try {
      // Get all active game lobbies
      const availableGames = Array.from(gameLobbies.entries())
        .filter(([_, lobby]) => lobby.gameStarted)
        .map(([code, lobby]) => ({
          gameCode: code,
          teamNames: lobby.teamNames,
          playerCount: lobby.players.length,
          spectatorCount: lobby.spectators ? lobby.spectators.length : 0
        }));

      // Also include active lobbies with their invite codes
      const activeLobbies = Array.from(lobbies.entries())
        .filter(([_, lobby]) =>
          lobby.gameStarted &&
          !lobby.redirectTo && // Skip redirect entries
          typeof lobby.partnerInviteCode === 'string' // Make sure it's a real lobby
        )
        .map(([code, lobby]) => ({
          gameCode: code,
          partnerInviteCode: lobby.partnerInviteCode,
          opponentInviteCode: lobby.opponentInviteCode,
          teamNames: lobby.teamNames || { 1: 'Team 1', 2: 'Team 2' },
          playerCount: lobby.players ? lobby.players.length : 0,
          spectatorCount: 0
        }));

      // Combine both lists
      const allGames = [...availableGames, ...activeLobbies];

      console.log(`Found ${allGames.length} available games to spectate`);
      callback({ success: true, games: allGames });
    } catch (error) {
      console.error('Error getting available games:', error);
      callback({ success: false, error: 'Failed to get available games' });
    }
  });

  // Create a new lobby
  socket.on('create_lobby', ({ playerName, teamName }, callback) => {
    try {
      const lobbyCode = generateLobbyCode();
      const partnerInviteCode = generateLobbyCode();
      const opponentInviteCode = generateLobbyCode();

      // Use default team name if not provided
      const team1Name = teamName || 'Team 1';

      // Create player object
      const player = {
        id: socket.id,
        name: playerName,
        avatar: getAvatarUrl(playerName),
        isHost: true,
        team: 1
      };

      // Create lobby
      lobbies.set(lobbyCode, {
        lobbyCode, // Store the lobby code in the lobby object for reference
        partnerInviteCode, // Code for inviting a partner (Team 1)
        opponentInviteCode, // Code for inviting opponents (Team 2)
        players: [player],
        gameStarted: false,
        teams: {
          1: [player],
          2: []
        },
        teamNames: {
          1: team1Name,
          2: 'Team 2'
        },
        teamReady: {
          1: false,
          2: false
        },
        isFindingMatch: false,
        matchedLobby: null,
        readyToStart: false
      });

      // Also store references to the invite codes for lookup
      lobbies.set(partnerInviteCode, { redirectTo: lobbyCode, teamToJoin: 1 });
      lobbies.set(opponentInviteCode, { redirectTo: lobbyCode, teamToJoin: 2 });

      // Associate socket with lobby
      socketToLobby.set(socket.id, lobbyCode);

      // Join socket room
      socket.join(lobbyCode);

      console.log(`Lobby created: ${lobbyCode} by ${playerName}`);
      console.log(`Partner invite code: ${partnerInviteCode}`);
      console.log(`Opponent invite code: ${opponentInviteCode}`);

      callback({
        success: true,
        lobbyCode,
        partnerInviteCode,
        opponentInviteCode
      });
    } catch (error) {
      console.error('Error creating lobby:', error);
      callback({ success: false, error: 'Failed to create lobby' });
    }
  });

  // Join an existing lobby
  socket.on('join_lobby', ({ lobbyCode, playerName }, callback) => {
    try {
      // Check if the code is a redirect (invite code)
      if (lobbies.has(lobbyCode) && lobbies.get(lobbyCode).redirectTo) {
        const inviteInfo = lobbies.get(lobbyCode);
        const actualLobbyCode = inviteInfo.redirectTo;
        const teamToJoin = inviteInfo.teamToJoin;

        console.log(`Invite code ${lobbyCode} redirects to lobby ${actualLobbyCode}, team ${teamToJoin}`);

        // Check if the actual lobby exists
        if (!lobbies.has(actualLobbyCode)) {
          return callback({ success: false, error: 'Lobby not found' });
        }

        const lobby = lobbies.get(actualLobbyCode);

        // Check if game has already started
        if (lobby.gameStarted) {
          return callback({ success: false, error: 'Game already in progress' });
        }

        // Check if the team is full
        if (lobby.teams[teamToJoin].length >= 2) {
          return callback({ success: false, error: `Team ${teamToJoin} is already full` });
        }

        // Create player object with the specified team
        const player = {
          id: socket.id,
          name: playerName,
          avatar: getAvatarUrl(playerName),
          isHost: false,
          team: teamToJoin
        };

        // Add player to lobby
        lobby.players.push(player);
        lobby.teams[teamToJoin].push(player);

        // Associate socket with the actual lobby code
        socketToLobby.set(socket.id, actualLobbyCode);

        // Join socket room
        socket.join(actualLobbyCode);

        // Notify all players in the lobby
        io.to(actualLobbyCode).emit('players_updated', {
          players: lobby.players,
          teams: lobby.teams,
          teamReady: lobby.teamReady
        });

        console.log(`Player ${playerName} joined lobby ${actualLobbyCode} on team ${teamToJoin} via invite code`);
        callback({
          success: true,
          actualLobbyCode,
          isInviteCode: true
        });

        return;
      }

      // Regular lobby code handling
      if (!lobbies.has(lobbyCode)) {
        return callback({ success: false, error: 'Lobby not found' });
      }

      const lobby = lobbies.get(lobbyCode);

      // Check if game already started
      if (lobby.gameStarted) {
        return callback({ success: false, error: 'Game already in progress' });
      }

      // Check if lobby is full
      if (lobby.players.length >= 4) {
        return callback({ success: false, error: 'Lobby is full' });
      }

      // Determine team (ensure each team has exactly 2 players)
      const team1Count = lobby.teams[1].length;
      const team2Count = lobby.teams[2].length;

      // If team 1 has less than 2 players, assign to team 1
      // Otherwise, assign to team 2
      const team = team1Count < 2 ? 1 : 2;

      // Check if lobby is full (both teams have 2 players)
      if (team1Count >= 2 && team2Count >= 2) {
        return callback({ success: false, error: 'Lobby is full (both teams have 2 players)' });
      }

      // Create player object
      const player = {
        id: socket.id,
        name: playerName,
        avatar: getAvatarUrl(playerName),
        isHost: false,
        team
      };

      // Add player to lobby
      lobby.players.push(player);
      lobby.teams[team].push(player);

      // Associate socket with lobby
      socketToLobby.set(socket.id, lobbyCode);

      // Join socket room
      socket.join(lobbyCode);

      // Notify all players in the lobby
      io.to(lobbyCode).emit('players_updated', {
        players: lobby.players,
        teams: lobby.teams,
        teamReady: lobby.teamReady
      });

      console.log(`Player ${playerName} joined lobby ${lobbyCode}`);
      callback({ success: true });
    } catch (error) {
      console.error('Error joining lobby:', error);
      callback({ success: false, error: 'Failed to join lobby' });
    }
  });

  // Update team name
  socket.on('update_team_name', ({ lobbyCode, teamNumber, teamName }, callback) => {
    try {
      // Check if lobby exists
      if (!lobbyCode && socketToLobby.has(socket.id)) {
        lobbyCode = socketToLobby.get(socket.id);
      }

      if (!lobbies.has(lobbyCode)) {
        return callback({ success: false, error: 'Lobby not found' });
      }

      const lobby = lobbies.get(lobbyCode);

      // Check if player is in the lobby
      const player = lobby.players.find(p => p.id === socket.id);
      if (!player) {
        return callback({ success: false, error: 'Player not found in lobby' });
      }

      // Only allow host to update team names
      if (!player.isHost) {
        return callback({ success: false, error: 'Only the host can update team names' });
      }

      // Validate team number
      if (teamNumber !== 1 && teamNumber !== 2) {
        return callback({ success: false, error: 'Invalid team number' });
      }

      // Update team name
      lobby.teamNames[teamNumber] = teamName || `Team ${teamNumber}`;

      // Notify all players in the lobby
      io.to(lobbyCode).emit('team_names_updated', { teamNames: lobby.teamNames });

      console.log(`Team ${teamNumber} name updated to "${teamName}" in lobby ${lobbyCode}`);
      callback({ success: true });
    } catch (error) {
      console.error('Error updating team name:', error);
      callback({ success: false, error: 'Failed to update team name' });
    }
  });

  // Set team ready status
  socket.on('set_team_ready', ({ lobbyCode, ready }, callback) => {
    try {
      // Check if lobby exists
      if (!lobbyCode && socketToLobby.has(socket.id)) {
        lobbyCode = socketToLobby.get(socket.id);
      }

      if (!lobbies.has(lobbyCode)) {
        return callback({ success: false, error: 'Lobby not found' });
      }

      const lobby = lobbies.get(lobbyCode);
      const player = lobby.players.find(p => p.id === socket.id);

      if (!player) {
        return callback({ success: false, error: 'Player not found in lobby' });
      }

      // Update player ready status
      player.isReady = ready;

      // Check if all players in the team are ready
      const teamPlayers = lobby.teams[player.team];
      const allTeamPlayersReady = teamPlayers.length > 0 && teamPlayers.every(p => p.isReady);

      // Set team ready status based on all players in the team
      lobby.teamReady[player.team] = allTeamPlayersReady;

      // Notify all players in the lobby
      io.to(lobbyCode).emit('team_ready_updated', {
        teamReady: lobby.teamReady,
        players: lobby.players
      });

      console.log(`Team ${player.team} ready status set to ${ready} in lobby ${lobbyCode}`);
      callback({ success: true });

      // Check if both teams are ready to start the game
      if (lobby.teamReady[1] && lobby.teamReady[2] &&
          lobby.teams[1].length === 2 && lobby.teams[2].length === 2) {
        // Start the game automatically
        lobby.gameStarted = true;

        // Initialize timeframe voting
        timeframeUtils.initTimeframeVoting(lobby);

        // Notify all players
        io.to(lobbyCode).emit('game_started', {
          players: lobby.players,
          teams: lobby.teams
        });

        // First transition to play timeframe voting phase
        io.to(lobbyCode).emit('game_phase_updated', {
          phase: 'play-timeframe-voting',
          players: lobby.players
        });

        // Register the game in the gameLobbies map for spectator access
        gameLobbies.set(lobbyCode, {
          ...lobby,
          spectators: [],
          originalLobbies: [lobbyCode]
        });

        console.log(`Game started in lobby ${lobbyCode} (both teams ready)`);
        console.log(`Game phase updated to play-timeframe-voting for lobby ${lobbyCode}`);
        console.log(`Registered game lobby ${lobbyCode} for spectator access`);
        console.log(`Available game lobbies: ${Array.from(gameLobbies.keys()).join(', ')}`);
      }
    } catch (error) {
      console.error('Error setting team ready status:', error);
      callback({ success: false, error: 'Failed to set team ready status' });
    }
  });

  // Start the game
  socket.on('start_game', ({ lobbyCode }, callback) => {
    try {
      // Check if lobby exists
      if (!lobbyCode && socketToLobby.has(socket.id)) {
        lobbyCode = socketToLobby.get(socket.id);
      }

      if (!lobbies.has(lobbyCode)) {
        return callback({ success: false, error: 'Lobby not found' });
      }

      const lobby = lobbies.get(lobbyCode);

      // Check if player is host
      const player = lobby.players.find(p => p.id === socket.id);
      if (!player || !player.isHost) {
        return callback({ success: false, error: 'Only the host can start the game' });
      }

      // Check if matched with another lobby
      if (!lobby.matchedLobby) {
        return callback({ success: false, error: 'Not matched with another team yet. Use Find Match first.' });
      }

      // Get the matched lobby
      const matchedLobby = lobbies.get(lobby.matchedLobby);
      if (!matchedLobby) {
        return callback({ success: false, error: 'Matched lobby not found' });
      }

      // Make sure there are exactly 2 players in team 1
      const team1Count = lobby.teams[1].length;
      if (team1Count !== 2) {
        return callback({ success: false, error: 'Team 1 must have exactly 2 players' });
      }

      // Mark this lobby as ready to start
      lobby.readyToStart = true;

      // Check if both lobbies are ready to start
      if (matchedLobby.readyToStart) {
        // Start the game for both lobbies
        lobby.gameStarted = true;
        matchedLobby.gameStarted = true;

        // Create combined player list with all players from both lobbies
        let allPlayers = [];

        // Add players from team 1 in both lobbies
        if (lobby.teams[1] && lobby.teams[1].length > 0) {
          allPlayers = [...allPlayers, ...lobby.teams[1]];
        }

        // Add players from team 2 in both lobbies
        if (lobby.teams[2] && lobby.teams[2].length > 0) {
          allPlayers = [...allPlayers, ...lobby.teams[2]];
        }

        // Add players from matched lobby team 1
        if (matchedLobby.teams[1] && matchedLobby.teams[1].length > 0) {
          allPlayers = [...allPlayers, ...matchedLobby.teams[1]];
        }

        // Add players from matched lobby team 2
        if (matchedLobby.teams[2] && matchedLobby.teams[2].length > 0) {
          allPlayers = [...allPlayers, ...matchedLobby.teams[2]];
        }

        // Sort by ID for consistent order across clients
        allPlayers = allPlayers.sort((a, b) => a.id.localeCompare(b.id));

        console.log('All players for game start:', allPlayers.map(p => p.name));

        // Assign team 2 to the matched lobby's players
        matchedLobby.teams[1].forEach(p => {
          p.team = 2;
        });

        // Notify all players in both lobbies
        io.to(lobbyCode).emit('game_started', {
          players: allPlayers,
          teams: {
            1: lobby.teams[1],
            2: matchedLobby.teams[1]
          },
          teamNames: {
            1: lobby.teamNames[1],
            2: matchedLobby.teamNames[1]
          }
        });

        io.to(matchedLobby.lobbyCode).emit('game_started', {
          players: allPlayers,
          teams: {
            1: lobby.teams[1],
            2: matchedLobby.teams[1]
          },
          teamNames: {
            1: lobby.teamNames[1],
            2: matchedLobby.teamNames[1]
          }
        });

        // Immediately transition to dealer determination phase
        io.to(lobbyCode).emit('game_phase_updated', {
          phase: 'dealer-determination',
          players: allPlayers
        });

        io.to(matchedLobby.lobbyCode).emit('game_phase_updated', {
          phase: 'dealer-determination',
          players: allPlayers
        });

        // Register the game in the gameLobbies map for spectator access
        // Use the original lobby code as the game lobby code
        gameLobbies.set(lobbyCode, {
          lobbyCode,
          players: allPlayers,
          teams: {
            1: lobby.teams[1],
            2: matchedLobby.teams[1]
          },
          teamNames: {
            1: lobby.teamNames[1],
            2: matchedLobby.teamNames[1]
          },
          gameStarted: true,
          spectators: [],
          originalLobbies: [lobbyCode, matchedLobby.lobbyCode]
        });

        console.log(`Game started between lobbies ${lobbyCode} and ${matchedLobby.lobbyCode}`);
        console.log(`Game phase updated to dealer-determination for lobbies ${lobbyCode} and ${matchedLobby.lobbyCode}`);
        console.log(`Registered game lobby ${lobbyCode} for spectator access`);
        console.log(`Available game lobbies: ${Array.from(gameLobbies.keys()).join(', ')}`);
      } else {
        // Notify the current lobby that we're waiting for the other team
        io.to(lobbyCode).emit('waiting_for_other_team');
        console.log(`Lobby ${lobbyCode} is ready to start, waiting for ${matchedLobby.lobbyCode}`);
      }

      callback({ success: true });
    } catch (error) {
      console.error('Error starting game:', error);
      callback({ success: false, error: 'Failed to start game' });
    }
  });

  // Find a match for the team
  socket.on('find_match', ({ lobbyCode }, callback) => {
    try {
      // Check if lobby exists
      if (!lobbyCode && socketToLobby.has(socket.id)) {
        lobbyCode = socketToLobby.get(socket.id);
      }

      if (!lobbies.has(lobbyCode)) {
        return callback({ success: false, error: 'Lobby not found' });
      }

      const lobby = lobbies.get(lobbyCode);

      // Check if both players in team 1 are ready
      if (!lobby.teamReady[1] || lobby.teams[1].length !== 2) {
        return callback({ success: false, error: 'Team 1 must have 2 ready players to find a match' });
      }

      // Check if already finding a match
      if (lobby.isFindingMatch) {
        return callback({ success: false, error: 'Already finding a match' });
      }

      // Check if already matched
      if (lobby.matchedLobby) {
        return callback({ success: false, error: 'Already matched with another team' });
      }

      // Mark as finding a match
      lobby.isFindingMatch = true;

      // Add to match queue
      matchQueue.add(lobbyCode);

      // Notify all players in the lobby
      io.to(lobbyCode).emit('players_updated', {
        players: lobby.players,
        isFindingMatch: true
      });

      // Force emit an event to update the client state
      io.to(lobbyCode).emit('match_status_update', {
        isFindingMatch: true,
        matchedLobby: null,
        matchedTeam: null
      });

      // Log the match queue for debugging
      console.log('Current match queue:', Array.from(matchQueue));

      // Force a check for matches immediately
      setTimeout(() => {
        // Only proceed if still looking for a match
        if (!lobbies.has(lobbyCode) || !lobbies.get(lobbyCode).isFindingMatch) {
          return;
        }

        console.log(`Forced match check for lobby ${lobbyCode}`);

        // Check if there's another lobby in the queue
        let matchFound = false;

        // Convert Set to Array for easier debugging and manipulation
        const queueArray = Array.from(matchQueue);
        console.log('Checking for matches among:', queueArray);

        // Skip if only one lobby in queue
        if (queueArray.length < 2) {
          console.log('Not enough lobbies in queue for matching');
          return;
        }

        for (const otherLobbyCode of queueArray) {
          // Skip self
          if (otherLobbyCode === lobbyCode) {
            console.log(`Skipping self (${lobbyCode})`);
            continue;
          }

          console.log(`Checking lobby ${otherLobbyCode} for match compatibility`);
          const otherLobby = lobbies.get(otherLobbyCode);

          if (!otherLobby) {
            console.log(`Lobby ${otherLobbyCode} not found in lobbies map`);
            // Clean up the queue if lobby doesn't exist
            matchQueue.delete(otherLobbyCode);
            continue;
          }

          console.log(`Lobby ${otherLobbyCode} status:`, {
            isFindingMatch: otherLobby.isFindingMatch,
            teamReady: otherLobby.teamReady[1],
            teamSize: otherLobby.teams[1].length,
            team1Players: otherLobby.teams[1].map(p => p.name)
          });

          // Check if other lobby is valid for matching
          if (otherLobby.isFindingMatch && otherLobby.teamReady[1] && otherLobby.teams[1].length === 2) {
            console.log(`Match found between ${lobbyCode} and ${otherLobbyCode}!`);

            // Get the current lobby again to ensure it's still valid
            const currentLobby = lobbies.get(lobbyCode);
            if (!currentLobby || !currentLobby.isFindingMatch) {
              console.log(`Lobby ${lobbyCode} is no longer valid for matching`);
              continue;
            }

            // Match found!
            matchFound = true;

            // Remove both from queue
            matchQueue.delete(lobbyCode);
            matchQueue.delete(otherLobbyCode);

            // Update both lobbies
            lobby.isFindingMatch = false;
            lobby.matchedLobby = otherLobbyCode;
            otherLobby.isFindingMatch = false;
            otherLobby.matchedLobby = lobbyCode;

            // Notify both lobbies with match_found event
            io.to(lobbyCode).emit('match_found', {
              matchedLobby: otherLobbyCode,
              matchedTeam: otherLobby.teams[1]
            });

            io.to(otherLobbyCode).emit('match_found', {
              matchedLobby: lobbyCode,
              matchedTeam: lobby.teams[1]
            });

            // Also send match_status_update for more reliable state updates
            io.to(lobbyCode).emit('match_status_update', {
              isFindingMatch: false,
              matchedLobby: otherLobbyCode,
              matchedTeam: otherLobby.teams[1]
            });

            io.to(otherLobbyCode).emit('match_status_update', {
              isFindingMatch: false,
              matchedLobby: lobbyCode,
              matchedTeam: lobby.teams[1]
            });

            break;
        }
      }

        console.log(`Team in lobby ${lobbyCode} is looking for a match. Match found: ${matchFound}`);
      }, 500); // Small delay to allow other lobbies to be added to the queue

      callback({ success: true });
    } catch (error) {
      console.error('Error finding match:', error);
      callback({ success: false, error: 'Failed to find match' });
    }
  });

  // Cancel finding a match
  socket.on('cancel_find_match', ({ lobbyCode }, callback) => {
    try {
      // Check if lobby exists
      if (!lobbyCode && socketToLobby.has(socket.id)) {
        lobbyCode = socketToLobby.get(socket.id);
      }

      if (!lobbies.has(lobbyCode)) {
        return callback({ success: false, error: 'Lobby not found' });
      }

      const lobby = lobbies.get(lobbyCode);

      // Check if finding a match
      if (!lobby.isFindingMatch) {
        return callback({ success: false, error: 'Not finding a match' });
      }

      // Remove from match queue
      matchQueue.delete(lobbyCode);

      // Update lobby
      lobby.isFindingMatch = false;

      // Notify all players in the lobby
      io.to(lobbyCode).emit('match_canceled');

      // Also send match_status_update for more reliable state updates
      io.to(lobbyCode).emit('match_status_update', {
        isFindingMatch: false,
        matchedLobby: null,
        matchedTeam: null
      });

      // Update players with the new status
      io.to(lobbyCode).emit('players_updated', {
        players: lobby.players,
        isFindingMatch: false
      });

      console.log(`Team in lobby ${lobbyCode} canceled finding a match`);
      callback({ success: true });
    } catch (error) {
      console.error('Error canceling match finding:', error);
      callback({ success: false, error: 'Failed to cancel match finding' });
    }
  });

  // Switch team
  socket.on('switch_team', ({ lobbyCode }, callback) => {
    try {
      // Check if lobby exists
      if (!lobbyCode && socketToLobby.has(socket.id)) {
        lobbyCode = socketToLobby.get(socket.id);
      }

      if (!lobbies.has(lobbyCode)) {
        return callback({ success: false, error: 'Lobby not found' });
      }

      const lobby = lobbies.get(lobbyCode);
      const player = lobby.players.find(p => p.id === socket.id);

      if (!player) {
        return callback({ success: false, error: 'Player not found in lobby' });
      }

      // Don't allow switching if game has started
      if (lobby.gameStarted) {
        return callback({ success: false, error: 'Cannot switch teams after game has started' });
      }

      // Remove player from current team
      const currentTeam = player.team;
      const newTeam = currentTeam === 1 ? 2 : 1;

      // Check if switching would result in more than 2 players in the new team
      if (lobby.teams[newTeam].length >= 2) {
        return callback({ success: false, error: 'Cannot switch teams - destination team is full (2 players maximum)' });
      }

      // Check if switching would leave the current team empty
      if (lobby.teams[currentTeam].length <= 1) {
        return callback({ success: false, error: 'Cannot switch teams - would leave current team empty' });
      }

      const teamIndex = lobby.teams[currentTeam].findIndex(p => p.id === socket.id);
      if (teamIndex !== -1) {
        lobby.teams[currentTeam].splice(teamIndex, 1);
      }

      // Add player to new team
      lobby.teams[newTeam].push(player);

      // Update player's team
      player.team = newTeam;

      // Reset team ready status when someone switches teams
      lobby.teamReady[currentTeam] = false;
      lobby.teamReady[newTeam] = false;

      // Notify all players in the lobby
      io.to(lobbyCode).emit('players_updated', {
        players: lobby.players,
        teams: lobby.teams,
        teamReady: lobby.teamReady
      });

      console.log(`Player ${player.name} switched from team ${currentTeam} to team ${newTeam} in lobby ${lobbyCode}`);
      callback({ success: true });
    } catch (error) {
      console.error('Error switching team:', error);
      callback({ success: false, error: 'Failed to switch team' });
    }
  });

  // Create a full game deck with card IDs
function createGameDeck() {
  const suits = ['hearts', 'diamonds', 'clubs', 'spades'];
  const values = ['9', '10', 'J', 'Q', 'K', 'A'];
  const points = {
    '9': 20,
    '10': 10,
    'J': 30,
    'Q': 2,
    'K': 3,
    'A': 11
  };

  let deck = [];
  let id = 1;

  for (const suit of suits) {
    for (const value of values) {
      deck.push({
        id: `card_${id++}`,
        suit,
        value,
        points: points[value],
        image: `/CardFace/${value === '10' ? 'T' : value}${suit.charAt(0).toUpperCase()}.svg`
      });
    }
  }

  // Shuffle the deck
  for (let i = deck.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [deck[i], deck[j]] = [deck[j], deck[i]];
  }

  return deck;
}

// Function to deal cards one at a time in anti-clockwise order
function dealCardsOneAtATime(lobby, matchedLobby, allPlayers, gameDeck, currentRound = 0, currentPlayerIndex = 0, playerHands = {}) {
  console.log(`dealCardsOneAtATime - Round: ${currentRound}, Player Index: ${currentPlayerIndex}`);

  // Initialize player hands if first round
  if (currentRound === 0 && currentPlayerIndex === 0) {
    console.log('Initializing player hands for the first time');
    allPlayers.forEach(player => {
      playerHands[player.id] = [];
      console.log(`Created empty hand for player ${player.name} (${player.id})`);
    });
  }

  // If we've dealt all 6 rounds to all players, we're done
  if (currentRound >= 6) {
    console.log('All 6 rounds of cards have been dealt, finishing up');

    // Store the remaining cards in the lobby for future deals
    lobby.remainingDeck = gameDeck;
    matchedLobby.remainingDeck = gameDeck;
    console.log(`Stored ${gameDeck.length} remaining cards in the lobby deck`);

    // Send each player their complete hand privately
    for (const playerId in playerHands) {
      const playerSocket = io.sockets.sockets.get(playerId);
      if (playerSocket) {
        playerSocket.emit('receive_cards', { cards: playerHands[playerId] });
        console.log(`Sent ${playerHands[playerId].length} cards to player ${playerId}`);
      } else {
        console.error(`Could not find socket for player ${playerId}`);
      }
    }

    // Notify all players that cards have been dealt
    console.log('Sending cards_dealt event to all players');
    io.to(lobby.lobbyCode).emit('cards_dealt', { dealerId: lobby.dealerId });
    io.to(matchedLobby.lobbyCode).emit('cards_dealt', { dealerId: lobby.dealerId });

    // Set the first player's turn (typically to the left of the dealer)
    const dealerIndex = allPlayers.findIndex(p => p.id === lobby.dealerId);
    const firstPlayerIndex = (dealerIndex + 1) % allPlayers.length;
    const firstPlayerId = allPlayers[firstPlayerIndex].id;
    console.log(`Setting first player turn to ${firstPlayerId}`);

    // Notify all players whose turn it is
    io.to(lobby.lobbyCode).emit('player_turn', { playerId: firstPlayerId });
    io.to(matchedLobby.lobbyCode).emit('player_turn', { playerId: firstPlayerId });
    return;
  }

  // Get the current player
  const currentPlayer = allPlayers[currentPlayerIndex];
  // console.log(`Dealing card to player ${currentPlayer.name} (${currentPlayer.id})`);

  // First, notify all clients that we're about to deal a card to this player
  console.log(`Sending dealing_card_to event for player ${currentPlayer.name}`);
  io.to(lobby.lobbyCode).emit('dealing_card_to', { playerId: currentPlayer.id });
  io.to(matchedLobby.lobbyCode).emit('dealing_card_to', { playerId: currentPlayer.id });

  // After a short delay, deal the actual card
  setTimeout(() => {
    // Deal one card to the current player
    const card = gameDeck.shift();
    playerHands[currentPlayer.id].push(card);
    console.log(`Dealt card ${card.value} of ${card.suit} to player ${currentPlayer.name}`);

    // Notify all players about the card being dealt
    const dealInfo = {
      playerId: currentPlayer.id,
      card: card,
      isDealer: false // This is not for dealer determination
    };

    // Send to both lobbies - this will display the card to all players
    console.log(`Sending card_dealt event for ${card.value} of ${card.suit} to player ${currentPlayer.name}`);
    io.to(lobby.lobbyCode).emit('card_dealt', dealInfo);
    io.to(matchedLobby.lobbyCode).emit('card_dealt', dealInfo);

    // Move to the next player in anti-clockwise order (right to left)
    // In a 4-player game, anti-clockwise means: 0 -> 3 -> 2 -> 1 -> 0
    let nextPlayerIndex = (currentPlayerIndex - 1);
    if (nextPlayerIndex < 0) nextPlayerIndex = allPlayers.length - 1;

    // If we've gone through all players, move to the next round
    let nextRound = currentRound;
    if (nextPlayerIndex === allPlayers.length - 1) {
      nextRound++;
      console.log(`Completed round ${currentRound}, moving to round ${nextRound}`);
    }

    // Schedule the next card deal after a delay
    console.log(`Scheduling next card deal to player index ${nextPlayerIndex} in round ${nextRound}`);
    setTimeout(() => {
      dealCardsOneAtATime(lobby, matchedLobby, allPlayers, gameDeck, nextRound, nextPlayerIndex, playerHands);
    }, 1000); // 1 second delay between deals
  }, 500); // 0.5 second delay before dealing the card
}

// Handle various game actions

  // Handle shuffle deck event
  socket.on('shuffle_deck', (data, callback) => {
    try {
      console.log('shuffle_deck event received from client:', socket.id, 'data:', data);

      // Get lobby code
      const lobbyCode = socketToLobby.get(socket.id);
      if (!lobbyCode || !lobbies.has(lobbyCode)) {
        console.error('Lobby not found for socket ID:', socket.id);
        return callback?.({ success: false, error: 'Lobby not found' });
      }

      const lobby = lobbies.get(lobbyCode);
      console.log(`Processing shuffle_deck in lobby ${lobbyCode}, dealer ID: ${lobby.dealerId}`);

      // Check if player is the dealer
      if (lobby.dealerId !== socket.id) {
        console.error(`Player ${socket.id} is not the dealer (dealer is ${lobby.dealerId})`);
        return callback?.({ success: false, error: 'Only the dealer can shuffle the deck' });
      }

      // Get the shuffle type from the data
      const shuffleType = data.shuffleType || 'cascade';
      console.log(`Shuffling deck with ${shuffleType} shuffle for lobby:`, lobbyCode);

      // Check if this is a single lobby with all players (invite code case)
      const team1Count = lobby.teams[1]?.length || 0;
      const team2Count = lobby.teams[2]?.length || 0;
      const totalPlayers = team1Count + team2Count;
      const isSingleLobby = totalPlayers === 4;

      let matchedLobbyCode = null;
      let matchedLobby = null;

      if (isSingleLobby) {
        console.log('Single lobby with all 4 players detected (invite code case)');
        // In the invite code case, we use the same lobby
        matchedLobbyCode = lobbyCode;
        matchedLobby = lobby;
      } else {
        // In the find match case, we need to get the matched lobby
        if (!lobby.matchedLobby) {
          console.error('No matched lobby found');
          return callback?.({ success: false, error: 'No matched lobby found' });
        }

        matchedLobbyCode = lobby.matchedLobby;
        matchedLobby = lobbies.get(matchedLobbyCode);
        if (!matchedLobby) {
          console.error('Matched lobby not found');
          return callback?.({ success: false, error: 'Matched lobby not found' });
        }
      }

      // Notify all players about the shuffle animation
      io.to(lobbyCode).emit('shuffle_animation', {
        shuffleType: shuffleType,
        dealerId: socket.id
      });

      // Only send to matched lobby if it's different from the main lobby
      if (!isSingleLobby) {
        io.to(matchedLobbyCode).emit('shuffle_animation', {
          shuffleType: shuffleType,
          dealerId: socket.id
        });
      }

      // After a short delay, emit shuffle complete event
      setTimeout(() => {
        io.to(lobbyCode).emit('shuffle_complete');

        // Only send to matched lobby if it's different from the main lobby
        if (!isSingleLobby) {
          io.to(matchedLobbyCode).emit('shuffle_complete');
        }
      }, 6000); // Longer delay to match the animation duration

      callback?.({ success: true });
    } catch (error) {
      console.error('Error in shuffle_deck:', error);
      callback?.({ success: false, error: error.message });
    }
  });

  // Handle request cut event
  socket.on('request_cut', (data, callback) => {
    try {
      console.log('request_cut event received from client:', socket.id, 'data:', data);

      // Get lobby code
      const lobbyCode = socketToLobby.get(socket.id);
      if (!lobbyCode || !lobbies.has(lobbyCode)) {
        console.error('Lobby not found for socket ID:', socket.id);
        return callback?.({ success: false, error: 'Lobby not found' });
      }

      const lobby = lobbies.get(lobbyCode);
      console.log(`Processing request_cut in lobby ${lobbyCode}, dealer ID: ${lobby.dealerId}`);

      // Check if player is the dealer
      if (lobby.dealerId !== socket.id) {
        console.error(`Player ${socket.id} is not the dealer (dealer is ${lobby.dealerId})`);
        return callback?.({ success: false, error: 'Only the dealer can request a cut' });
      }

      // Validate the player to cut
      const playerToCut = data.playerId;
      if (!playerToCut) {
        console.error('No player specified to cut the deck');
        return callback?.({ success: false, error: 'No player specified to cut the deck' });
      }

      // Check if this is a single lobby with all players (invite code case)
      const team1Count = lobby.teams[1]?.length || 0;
      const team2Count = lobby.teams[2]?.length || 0;
      const totalPlayers = team1Count + team2Count;
      const isSingleLobby = totalPlayers === 4;

      let matchedLobbyCode = null;
      let matchedLobby = null;

      if (isSingleLobby) {
        console.log('Single lobby with all 4 players detected (invite code case)');
        // In the invite code case, we use the same lobby
        matchedLobbyCode = lobbyCode;
        matchedLobby = lobby;
      } else {
        // In the find match case, we need to get the matched lobby
        if (!lobby.matchedLobby) {
          console.error('No matched lobby found');
          return callback?.({ success: false, error: 'No matched lobby found' });
        }

        matchedLobbyCode = lobby.matchedLobby;
        matchedLobby = lobbies.get(matchedLobbyCode);
        if (!matchedLobby) {
          console.error('Matched lobby not found');
          return callback?.({ success: false, error: 'Matched lobby not found' });
        }
      }

      // Send cut request to the specified player in both lobbies
      console.log('Requesting cut from player:', playerToCut);
      io.to(lobbyCode).emit('cut_requested', { playerId: playerToCut });

      // Only send to matched lobby if it's different from the main lobby
      if (!isSingleLobby) {
        io.to(matchedLobbyCode).emit('cut_requested', { playerId: playerToCut });
      }

      // Also send a direct message to the player who needs to cut
      io.to(playerToCut).emit('cut_requested', { playerId: playerToCut });

      callback?.({ success: true });
    } catch (error) {
      console.error('Error in request_cut:', error);
      callback?.({ success: false, error: error.message });
    }
  });

  // Handle cut deck event
  socket.on('cut_deck', (data, callback) => {
    try {
      console.log('cut_deck event received from client:', socket.id, 'data:', data);

      // Get lobby code
      const lobbyCode = socketToLobby.get(socket.id);
      if (!lobbyCode || !lobbies.has(lobbyCode)) {
        console.error('Lobby not found for socket ID:', socket.id);
        return callback?.({ success: false, error: 'Lobby not found' });
      }

      const lobby = lobbies.get(lobbyCode);

      // Check if this is a single lobby with all players (invite code case)
      const team1Count = lobby.teams[1]?.length || 0;
      const team2Count = lobby.teams[2]?.length || 0;
      const totalPlayers = team1Count + team2Count;
      const isSingleLobby = totalPlayers === 4;

      let matchedLobbyCode = null;
      let matchedLobby = null;

      if (isSingleLobby) {
        console.log('Single lobby with all 4 players detected (invite code case)');
        // In the invite code case, we use the same lobby
        matchedLobbyCode = lobbyCode;
        matchedLobby = lobby;
      } else {
        // In the find match case, we need to get the matched lobby
        if (!lobby.matchedLobby) {
          console.error('No matched lobby found');
          return callback?.({ success: false, error: 'No matched lobby found' });
        }

        matchedLobbyCode = lobby.matchedLobby;
        matchedLobby = lobbies.get(matchedLobbyCode);
        if (!matchedLobby) {
          console.error('Matched lobby not found');
          return callback?.({ success: false, error: 'Matched lobby not found' });
        }
      }

      // Process the cut decision
      const didCut = data.cut === true;
      const cutPosition = data.position || null;
      console.log(`Player ${socket.id} ${didCut ? `cut the deck at ${cutPosition}` : 'did not cut'} the deck`);

      // Store the player who cut the deck
      if (didCut) {
        lobby.lastCutPlayerId = socket.id;
        matchedLobby.lastCutPlayerId = socket.id;
        console.log(`Stored last cut player ID: ${socket.id}`);
      }

      // Notify all players in both lobbies that the cut is complete
      // Include the position in the event data so all players can see the animation
      io.to(lobbyCode).emit('cut_complete', {
        cut: didCut,
        position: cutPosition,
        playerId: socket.id
      });

      // Only send to matched lobby if it's different from the main lobby
      if (!isSingleLobby) {
        io.to(matchedLobbyCode).emit('cut_complete', {
          cut: didCut,
          position: cutPosition,
          playerId: socket.id
        });
      }

      callback?.({ success: true });
    } catch (error) {
      console.error('Error in cut_deck:', error);
      callback?.({ success: false, error: error.message });
    }
  });

  // Handle deal first four event
  socket.on('deal_first_four', (data, callback) => {
    try {
      console.log('deal_first_four event received from client:', socket.id);

      // Check if player is in a game lobby first
      let gameLobbyCode = socketToGameLobby.get(socket.id);
      let lobby, matchedLobby, lobbyCode;

      if (gameLobbyCode && gameLobbies.has(gameLobbyCode)) {
        // Player is in a game lobby
        const gameLobby = gameLobbies.get(gameLobbyCode);
        console.log(`Player ${socket.id} is in game lobby ${gameLobbyCode}`);

        // Use the game lobby for all operations
        lobby = gameLobby;
        matchedLobby = gameLobby; // Same lobby for both
        lobbyCode = gameLobbyCode;
      } else {
        // Fall back to regular lobby
        lobbyCode = socketToLobby.get(socket.id);
        if (!lobbyCode || !lobbies.has(lobbyCode)) {
          console.error('Lobby not found for socket ID:', socket.id);
          return callback?.({ success: false, error: 'Lobby not found' });
        }

        lobby = lobbies.get(lobbyCode);
      }
      console.log(`Processing deal_first_four in lobby ${lobbyCode}, dealer ID: ${lobby.dealerId}`);

      // Check if player is the dealer
      if (lobby.dealerId !== socket.id) {
        console.error(`Player ${socket.id} is not the dealer (dealer is ${lobby.dealerId})`);
        return callback?.({ success: false, error: 'Only the dealer can deal cards' });
      }

      // If we're not using a game lobby, check if this is a single lobby with all players (invite code case)
      if (!gameLobbyCode) {
        const team1Count = lobby.teams[1]?.length || 0;
        const team2Count = lobby.teams[2]?.length || 0;
        const totalPlayers = team1Count + team2Count;
        const isSingleLobby = totalPlayers === 4;

        if (isSingleLobby) {
          console.log('Single lobby with all 4 players detected (invite code case)');
          // In the invite code case, we use the same lobby
          matchedLobby = lobby;
        } else {
          // In the find match case, we need to get the matched lobby
          if (!lobby.matchedLobby) {
            console.error('No matched lobby found');
            return callback?.({ success: false, error: 'Not matched with another team' });
          }

          matchedLobby = lobbies.get(lobby.matchedLobby);
          if (!matchedLobby) {
            console.error(`Matched lobby ${lobby.matchedLobby} not found`);
            return callback?.({ success: false, error: 'Matched lobby not found' });
          }
        }
      }

      // Deal first 4 cards to each player in the correct order
      console.log('Dealing first 4 cards to players in lobby:', lobbyCode);

      // TODO: Implement the actual dealing logic with the correct order
      // For now, just simulate it with a delay
      setTimeout(() => {
        // Deal 4 cards to each player
        dealingUtils.dealCardsToAllPlayers(io, lobby, matchedLobby, 4);

        // Determine the trump selector (player to the right of the dealer)
        // This should be the partner of the player who cut the deck
        const dealerTeam = lobby.players.find(p => p.id === lobby.dealerId)?.team;
        const oppositeTeam = dealerTeam === 1 ? 2 : 1;

        // Get all players from both lobbies
        const allPlayers = [...lobby.players, ...matchedLobby.players];

        // The trump selector should be the player to the right of the dealer
        // In Thunee, this is the partner of the player who cut the deck

        // For debugging, log all players and their teams
        console.log('All players:', allPlayers.map(p => `${p.name} (${p.id}) - Team ${p.team}`));
        console.log('Dealer ID:', lobby.dealerId);
        console.log('Last cut player ID:', lobby.lastCutPlayerId);

        // Find the player who cut the deck
        const cutPlayer = allPlayers.find(p => p.id === lobby.lastCutPlayerId);
        console.log('Cut player:', cutPlayer ? `${cutPlayer.name} (${cutPlayer.id}) - Team ${cutPlayer.team}` : 'Not found');

        // Find the partner of the player who cut the deck (same team, different player)
        let trumpSelector = null;

        if (cutPlayer) {
          trumpSelector = allPlayers.find(p => p.team === cutPlayer.team && p.id !== cutPlayer.id);
          console.log('Trump selector (partner of cut player):',
            trumpSelector ? `${trumpSelector.name} (${trumpSelector.id}) - Team ${trumpSelector.team}` : 'Not found');
        }

        // Fallback: if we couldn't find the trump selector, pick any player from the opposite team
        if (!trumpSelector) {
          console.log('Using fallback to find trump selector');
          trumpSelector = allPlayers.find(p => p.team === oppositeTeam);
          console.log('Fallback trump selector:',
            trumpSelector ? `${trumpSelector.name} (${trumpSelector.id}) - Team ${trumpSelector.team}` : 'Not found');
        }

        if (trumpSelector) {
          console.log(`Setting trump selector to ${trumpSelector.id} (${trumpSelector.name})`);
          lobby.trumpSelectorId = trumpSelector.id;
          matchedLobby.trumpSelectorId = trumpSelector.id;

          // Notify all players about the trump selector
          io.to(lobbyCode).emit('trump_selector_assigned', { playerId: trumpSelector.id });
          io.to(matchedLobby.lobbyCode).emit('trump_selector_assigned', { playerId: trumpSelector.id });
        } else {
          console.error('Could not find a player from the opposite team to be the trump selector');
        }

        // Wait 5 seconds to ensure all cards are dealt before proceeding to trump selection
        // This gives players time to see their cards before trump selection
        setTimeout(() => {
          console.log('All cards have been dealt, proceeding to trump selection phase after 5 second delay');

          // Notify all players that the first four cards have been dealt
          // This will trigger the bidding phase on the client
          io.to(lobbyCode).emit('first_four_dealt');
          io.to(matchedLobby.lobbyCode).emit('first_four_dealt');

          // Update game phase to bidding
          io.to(lobbyCode).emit('game_phase_updated', { phase: 'bidding' });
          io.to(matchedLobby.lobbyCode).emit('game_phase_updated', { phase: 'bidding' });

          // Initialize bidding state
          lobby.currentBid = 0;
          lobby.highestBidder = null;
          lobby.biddingComplete = false;
          lobby.passedPlayers = new Set();
          matchedLobby.currentBid = 0;
          matchedLobby.highestBidder = null;
          matchedLobby.biddingComplete = false;
          matchedLobby.passedPlayers = new Set();

          // Set bidding order according to Thunee rules
          const dealerIndex = allPlayers.findIndex(p => p.id === lobby.dealerId);
          lobby.biddingOrder = [];
          matchedLobby.biddingOrder = [];

          // Get dealer's team
          const dealer = allPlayers[dealerIndex];
          const dealerTeam = dealer.team;

          // Initialize teams that can bid
          lobby.teamsAllowedToBid = new Set([dealerTeam]);
          matchedLobby.teamsAllowedToBid = new Set([dealerTeam]);

          // Track if dealer and partner have passed
          lobby.dealerTeamPassed = false;
          matchedLobby.dealerTeamPassed = false;

          // First, add dealer and their partner to the bidding order
          const dealerPartnerIndex = allPlayers.findIndex(p => p.team === dealerTeam && p.id !== dealer.id);

          // Add dealer and partner first
          lobby.biddingOrder.push(dealer.id);
          matchedLobby.biddingOrder.push(dealer.id);

          if (dealerPartnerIndex !== -1) {
            lobby.biddingOrder.push(allPlayers[dealerPartnerIndex].id);
            matchedLobby.biddingOrder.push(allPlayers[dealerPartnerIndex].id);
          }

          // Then add the other team's players
          const otherTeam = dealerTeam === 1 ? 2 : 1;
          const otherTeamPlayers = allPlayers.filter(p => p.team === otherTeam);

          // Add the initial trumper (player to the right of dealer) first
          const initialTrumperIndex = (dealerIndex + 3) % allPlayers.length;
          const initialTrumper = allPlayers[initialTrumperIndex];

          if (initialTrumper && initialTrumper.team === otherTeam) {
            lobby.biddingOrder.push(initialTrumper.id);
            matchedLobby.biddingOrder.push(initialTrumper.id);

            // Add the other player from the opposing team
            const otherOpposingPlayer = otherTeamPlayers.find(p => p.id !== initialTrumper.id);
            if (otherOpposingPlayer) {
              lobby.biddingOrder.push(otherOpposingPlayer.id);
              matchedLobby.biddingOrder.push(otherOpposingPlayer.id);
            }
          } else {
            // Just add both opposing team players in any order
            otherTeamPlayers.forEach(player => {
              lobby.biddingOrder.push(player.id);
              matchedLobby.biddingOrder.push(player.id);
            });
          }

          console.log('Bidding order:', lobby.biddingOrder.map(id => {
            const player = allPlayers.find(p => p.id === id);
            return `${player?.name} (Team ${player?.team})`;
          }));

          lobby.currentBidderIndex = 0;
          matchedLobby.currentBidderIndex = 0;

          // Notify first player it's their turn to bid
          const firstBidder = lobby.biddingOrder[0];
          io.to(firstBidder).emit('your_turn_to_bid');

          console.log(`Starting bidding phase with first bidder: ${firstBidder}`);
        }, 5000); // Wait 5 seconds after dealing cards
      }, 1000);

      callback?.({ success: true });
    } catch (error) {
      console.error('Error in deal_first_four:', error);
      callback?.({ success: false, error: error.message });
    }
  });

  // Handle place bid event
  socket.on('place_bid', (data, callback) => {
    try {
      console.log('place_bid event received from client:', socket.id, 'data:', data);

      // Get lobby code
      const lobbyCode = socketToLobby.get(socket.id);
      if (!lobbyCode || !lobbies.has(lobbyCode)) {
        console.error('Lobby not found for socket ID:', socket.id);
        return callback?.({ success: false, error: 'Lobby not found' });
      }

      const lobby = lobbies.get(lobbyCode);

      // Check if this is a single lobby with all players (invite code case)
      const team1Count = lobby.teams[1]?.length || 0;
      const team2Count = lobby.teams[2]?.length || 0;
      const totalPlayers = team1Count + team2Count;
      const isSingleLobby = totalPlayers === 4;

      let matchedLobbyCode;
      let matchedLobby;

      if (isSingleLobby) {
        console.log('Single lobby with all 4 players detected (invite code case)');
        // In the invite code case, we use the same lobby
        matchedLobbyCode = lobbyCode;
        matchedLobby = lobby;
      } else {
        // In the find match case, we need to get the matched lobby
        if (!lobby.matchedLobby) {
          console.error('No matched lobby found');
          return callback?.({ success: false, error: 'No matched lobby found' });
        }

        matchedLobbyCode = lobby.matchedLobby;
        matchedLobby = lobbies.get(matchedLobbyCode);
        if (!matchedLobby) {
          console.error('Matched lobby not found');
          return callback?.({ success: false, error: 'Matched lobby not found' });
        }
      }

      // Validate bid
      const bid = parseInt(data.bid);
      if (isNaN(bid) || bid <= 0) {
        console.error('Invalid bid value:', data.bid);
        return callback?.({ success: false, error: 'Invalid bid value' });
      }

      // Check if bid is valid (must be higher than current bid)
      if (bid <= lobby.currentBid) {
        console.error(`Bid ${bid} is not higher than current bid ${lobby.currentBid}`);
        return callback?.({ success: false, error: 'Bid must be higher than current bid' });
      }

      // Check if bid is one of the valid bid values (10, 20, 30, ..., 100, 104)
      const validBids = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 104];
      if (!validBids.includes(bid)) {
        console.error(`Bid ${bid} is not a valid bid value`);
        return callback?.({ success: false, error: 'Bid must be one of the valid bid values' });
      }

      // Check if it's this player's turn to bid
      const currentBidder = lobby.biddingOrder[lobby.currentBidderIndex];
      if (socket.id !== currentBidder) {
        console.error(`Not player ${socket.id}'s turn to bid. Current bidder is ${currentBidder}`);
        return callback?.({ success: false, error: 'Not your turn to bid' });
      }

      // Get player info
      const player = lobby.players.find(p => p.id === socket.id) ||
                    matchedLobby.players.find(p => p.id === socket.id);

      if (!player) {
        console.error(`Player ${socket.id} not found in either lobby`);
        return callback?.({ success: false, error: 'Player not found' });
      }

      // Check if the bid amount is valid based on the team
      const playerTeam = player.team;

      // Maximum bid is 100 for the bidding team, 104 for the opposing team
      // We need to determine if this player is on the same team as the initial trumper
      const initialTrumperTeam = getInitialTrumperTeam(lobby, matchedLobby);

      if (initialTrumperTeam !== null) {
        const isOpposingTeam = playerTeam !== initialTrumperTeam;

        // Check bid limits based on team
        if (!isOpposingTeam && bid > 100) {
          console.error(`Bid ${bid} exceeds maximum of 100 for the bidding team`);
          return callback?.({ success: false, error: 'Bid exceeds maximum of 100 for your team' });
        }

        if (isOpposingTeam && bid > 104) {
          console.error(`Bid ${bid} exceeds maximum of 104 for the opposing team`);
          return callback?.({ success: false, error: 'Bid exceeds maximum of 104 for your team' });
        }
      }

      // Initialize teamsThatBid if it doesn't exist
      if (!lobby.teamsThatBid) {
        lobby.teamsThatBid = new Set();
        matchedLobby.teamsThatBid = new Set();
      }

      // Check if this player is outbidding their teammate
      if (lobby.highestBidder) {
        // Get all players from both lobbies
        const allPlayers = [];
        if (lobby.players && Array.isArray(lobby.players)) {
          allPlayers.push(...lobby.players);
        }
        if (matchedLobby && matchedLobby.players && Array.isArray(matchedLobby.players)) {
          allPlayers.push(...matchedLobby.players);
        }

        const highestBidder = allPlayers.find(p => p.id === lobby.highestBidder);
        if (highestBidder && highestBidder.team === playerTeam) {
          console.log(`Player ${player.name} is outbidding their teammate ${highestBidder.name}`);
        }
      }

      // Add this player's team to the teams that have bid
      lobby.teamsThatBid.add(playerTeam);
      matchedLobby.teamsThatBid.add(playerTeam);

      // Update game state
      lobby.currentBid = bid;
      lobby.highestBidder = socket.id;
      matchedLobby.currentBid = bid;
      matchedLobby.highestBidder = socket.id;

      // Broadcast bid to all players
      const bidData = {
        playerId: socket.id,
        playerName: player.name,
        playerTeam: playerTeam,
        bid: bid
      };

      io.to(lobbyCode).emit('bid_placed', bidData);
      io.to(matchedLobbyCode).emit('bid_placed', bidData);

      // Move to next bidder
      moveToNextBidder(lobby, matchedLobby);

      callback?.({ success: true });
    } catch (error) {
      console.error('Error in place_bid:', error);
      callback?.({ success: false, error: error.message });
    }
  });

  // Handle pass bid event
  socket.on('pass_bid', (_, callback) => {
    try {
      console.log('pass_bid event received from client:', socket.id);

      // Get lobby code
      const lobbyCode = socketToLobby.get(socket.id);
      if (!lobbyCode || !lobbies.has(lobbyCode)) {
        console.error('Lobby not found for socket ID:', socket.id);
        return callback?.({ success: false, error: 'Lobby not found' });
      }

      const lobby = lobbies.get(lobbyCode);

      // Check if this is a single lobby with all players (invite code case)
      const team1Count = lobby.teams[1]?.length || 0;
      const team2Count = lobby.teams[2]?.length || 0;
      const totalPlayers = team1Count + team2Count;
      const isSingleLobby = totalPlayers === 4;

      let matchedLobbyCode;
      let matchedLobby;

      if (isSingleLobby) {
        console.log('Single lobby with all 4 players detected (invite code case)');
        // In the invite code case, we use the same lobby
        matchedLobbyCode = lobbyCode;
        matchedLobby = lobby;
      } else {
        // In the find match case, we need to get the matched lobby
        if (!lobby.matchedLobby) {
          console.error('No matched lobby found');
          return callback?.({ success: false, error: 'No matched lobby found' });
        }

        matchedLobbyCode = lobby.matchedLobby;
        matchedLobby = lobbies.get(matchedLobbyCode);
        if (!matchedLobby) {
          console.error('Matched lobby not found');
          return callback?.({ success: false, error: 'Matched lobby not found' });
        }
      }

      // Check if it's this player's turn to bid
      const currentBidder = lobby.biddingOrder[lobby.currentBidderIndex];
      if (socket.id !== currentBidder) {
        console.error(`Not player ${socket.id}'s turn to bid. Current bidder is ${currentBidder}`);
        return callback?.({ success: false, error: 'Not your turn to bid' });
      }

      // Mark player as passed
      lobby.passedPlayers.add(socket.id);
      matchedLobby.passedPlayers.add(socket.id);

      // Get player name
      const player = lobby.players.find(p => p.id === socket.id) ||
                    matchedLobby.players.find(p => p.id === socket.id);

      if (!player) {
        console.error(`Player ${socket.id} not found in either lobby`);
        return callback?.({ success: false, error: 'Player not found' });
      }

      // Broadcast pass to all players
      const passData = {
        playerId: socket.id,
        playerName: player.name
      };

      io.to(lobbyCode).emit('bid_passed', passData);
      io.to(matchedLobbyCode).emit('bid_passed', passData);

      // Move to next bidder
      moveToNextBidder(lobby, matchedLobby);

      callback?.({ success: true });
    } catch (error) {
      console.error('Error in pass_bid:', error);
      callback?.({ success: false, error: error.message });
    }
  });

  // Helper function to move to the next bidder
  function moveToNextBidder(lobby, matchedLobby) {
    // Check if bidding is complete
    if (lobby.passedPlayers.size >= lobby.biddingOrder.length - 1) {
      // Only one player left, bidding is complete
      completeBidding(lobby, matchedLobby);
      return;
    }

    // Get all players from both lobbies
    const allPlayers = [];
    if (lobby.players && Array.isArray(lobby.players)) {
      allPlayers.push(...lobby.players);
    }
    if (matchedLobby && matchedLobby.players && Array.isArray(matchedLobby.players)) {
      allPlayers.push(...matchedLobby.players);
    }

    // Get the current bidder's team
    const currentBidderIndex = lobby.currentBidderIndex;
    const currentBidderId = lobby.biddingOrder[currentBidderIndex];
    const currentBidder = allPlayers.find(p => p.id === currentBidderId);

    if (!currentBidder) {
      console.error(`Current bidder ${currentBidderId} not found in players list`);
      completeBidding(lobby, matchedLobby);
      return;
    }

    const currentBidderTeam = currentBidder.team;
    const dealerTeam = allPlayers.find(p => p.id === lobby.dealerId)?.team;
    const opposingTeam = dealerTeam === 1 ? 2 : 1;

    // Check if the dealer team has passed
    if (currentBidderTeam === dealerTeam && !lobby.highestBidder) {
      // If this is a dealer team player and they passed, check if both dealer team players have passed
      const dealerTeamPlayers = allPlayers.filter(p => p.team === dealerTeam);
      const dealerTeamPassedCount = dealerTeamPlayers.filter(p => lobby.passedPlayers.has(p.id)).length;

      if (dealerTeamPassedCount === dealerTeamPlayers.length) {
        console.log('Both dealer team players have passed, marking dealer team as passed');
        lobby.dealerTeamPassed = true;
        if (matchedLobby) matchedLobby.dealerTeamPassed = true;

        // If dealer team passed, allow the initial trumper to select trump
        // The initial trumper is the player to the right of the dealer
        // Import the playerPositionUtils
        const playerPositionUtils = require('./src/utils/server/playerPositionUtils');

        // Log players by their assigned positions
        console.log("Players by assigned positions in moveToNextBidder:");
        for (let pos = 1; pos <= 4; pos++) {
          const playerAtPosition = allPlayers.find(p => p.position === pos);
          if (playerAtPosition) {
            console.log(`Position ${pos}: ${playerAtPosition.name} (${playerAtPosition.id}) - Team ${playerAtPosition.team}`);
          } else {
            console.log(`Position ${pos}: Not assigned`);
          }
        }

        // Get the initial trumper position (position 2, to the right of dealer at position 1)
        const initialTrumperPosition = playerPositionUtils.getInitialTrumperPosition();
        console.log(`Looking for player at position ${initialTrumperPosition} (initial trumper)`);

        // Check if a trump selector was already set (partner of the player who cut the deck)
        if (lobby.trumpSelectorId) {
          const trumpSelector = allPlayers.find(p => p.id === lobby.trumpSelectorId);
          if (trumpSelector) {
            console.log(`Using pre-determined trump selector: ${trumpSelector.name} (${trumpSelector.id}) - Team ${trumpSelector.team} - Position ${trumpSelector.position}`);
            console.log(`Both dealer team players passed, keeping pre-determined trump selector ${trumpSelector.name}`);
            // Keep the existing trumpSelectorId
          } else {
            console.log(`Pre-determined trump selector ${lobby.trumpSelectorId} not found in player list, falling back to position-based approach`);

            // Find the player with the initial trumper position
            const initialTrumper = allPlayers.find(p => p.position === initialTrumperPosition);

            if (initialTrumper) {
              console.log(`Found initial trumper: ${initialTrumper.name} (${initialTrumper.id}) - Team ${initialTrumper.team} - Position ${initialTrumper.position}`);
              console.log(`Both dealer team players passed, allowing initial trumper ${initialTrumper.name} to select trump`);
              lobby.trumpSelectorId = initialTrumper.id;
              if (matchedLobby) matchedLobby.trumpSelectorId = initialTrumper.id;
            }
          }
        } else {
          // Find the player with the initial trumper position
          const initialTrumper = allPlayers.find(p => p.position === initialTrumperPosition);

          if (initialTrumper) {
            console.log(`Found initial trumper: ${initialTrumper.name} (${initialTrumper.id}) - Team ${initialTrumper.team} - Position ${initialTrumper.position}`);
            console.log(`Both dealer team players passed, allowing initial trumper ${initialTrumper.name} to select trump`);
            lobby.trumpSelectorId = initialTrumper.id;
            if (matchedLobby) matchedLobby.trumpSelectorId = initialTrumper.id;
          } else {
            // Fallback to old method if position-based approach fails
            const dealerIndex = allPlayers.findIndex(p => p.id === lobby.dealerId);
            const initialTrumperIndex = (dealerIndex + 3) % allPlayers.length;
            const fallbackInitialTrumper = allPlayers[initialTrumperIndex];

            console.log(`Dealer index: ${dealerIndex}, Initial trumper index: ${initialTrumperIndex}`);
            console.log(`Fallback initial trumper: ${fallbackInitialTrumper ? fallbackInitialTrumper.name : 'Not found'} (${fallbackInitialTrumper ? fallbackInitialTrumper.id : 'N/A'}) - Team ${fallbackInitialTrumper ? fallbackInitialTrumper.team : 'N/A'}`);

            if (fallbackInitialTrumper) {
              console.log(`Both dealer team players passed, allowing fallback initial trumper ${fallbackInitialTrumper.name} to select trump`);
              lobby.trumpSelectorId = fallbackInitialTrumper.id;
              if (matchedLobby) matchedLobby.trumpSelectorId = fallbackInitialTrumper.id;
            }
          }

          // Complete bidding and move to trump selection
          completeBidding(lobby, matchedLobby);
          return;
        }
      }
    }

    // If dealer team has bid, allow opposing team to bid
    if (lobby.highestBidder) {
      const highestBidder = allPlayers.find(p => p.id === lobby.highestBidder);

      if (highestBidder && highestBidder.team === dealerTeam) {
        // Dealer team has bid, allow opposing team to bid
        lobby.teamsAllowedToBid.add(opposingTeam);
        if (matchedLobby) matchedLobby.teamsAllowedToBid.add(opposingTeam);
        console.log(`Dealer team has bid, allowing opposing team ${opposingTeam} to bid`);
      }
    }

    // If there's a highest bidder, check if they're from the same team as the current bidder
    if (lobby.highestBidder) {
      const highestBidder = allPlayers.find(p => p.id === lobby.highestBidder);

      if (highestBidder && highestBidder.team === currentBidderTeam) {
        // If the highest bidder is from the same team as the current bidder,
        // we need to find a bidder from the opposite team
        console.log(`Highest bidder ${highestBidder.name} is from the same team as current bidder ${currentBidder.name}`);

        // Track which teams have already bid
        if (!lobby.teamsThatBid) {
          lobby.teamsThatBid = new Set();
          if (matchedLobby) matchedLobby.teamsThatBid = new Set();
        }

        // Add the current bidder's team to the teams that have bid
        lobby.teamsThatBid.add(currentBidderTeam);
        if (matchedLobby) matchedLobby.teamsThatBid.add(currentBidderTeam);
      }
    }

    // Find next bidder who hasn't passed and follows the team rules
    let nextBidderFound = false;
    let attempts = 0;

    while (!nextBidderFound && attempts < lobby.biddingOrder.length) {
      // Move to the next player in the bidding order
      lobby.currentBidderIndex = (lobby.currentBidderIndex + 1) % lobby.biddingOrder.length;
      if (matchedLobby) matchedLobby.currentBidderIndex = lobby.currentBidderIndex;

      const nextBidderId = lobby.biddingOrder[lobby.currentBidderIndex];
      const nextBidder = allPlayers.find(p => p.id === nextBidderId);

      if (!nextBidder) {
        console.error(`Next bidder ${nextBidderId} not found in players list`);
        attempts++;
        continue;
      }

      // Check if this player has already passed
      if (lobby.passedPlayers.has(nextBidderId)) {
        attempts++;
        continue;
      }

      // Check if this player's team is allowed to bid
      if (!lobby.teamsAllowedToBid.has(nextBidder.team)) {
        console.log(`Skipping player ${nextBidder.name} because their team ${nextBidder.team} is not allowed to bid yet`);

        // Mark this player as passed for this round
        lobby.passedPlayers.add(nextBidderId);
        if (matchedLobby) matchedLobby.passedPlayers.add(nextBidderId);

        // Notify all players that this player has passed
        const passData = {
          playerId: nextBidderId,
          playerName: nextBidder.name,
          autoPass: true,
          reason: 'Team not allowed to bid yet'
        };

        io.to(lobby.lobbyCode).emit('bid_passed', passData);
        if (matchedLobby) io.to(matchedLobby.lobbyCode).emit('bid_passed', passData);

        attempts++;
        continue;
      }

      // Check if a teammate of this player has already bid
      // According to the rules, only one player per team may bid
      if (lobby.teamsThatBid && lobby.teamsThatBid.has(nextBidder.team)) {
        // Check if the highest bidder is from this player's team
        const highestBidder = allPlayers.find(p => p.id === lobby.highestBidder);

        if (highestBidder && highestBidder.team === nextBidder.team) {
          // If the highest bidder is from this player's team, this player can't outbid them
          // But they still need to explicitly pass
          console.log(`Player ${nextBidder.name}'s teammate ${highestBidder.name} is the highest bidder, allowing them to explicitly pass`);
          nextBidderFound = true;

          // Notify next bidder it's their turn
          console.log(`Notifying player ${nextBidder.name} (${nextBidderId}) it's their turn to bid (expected to pass)`);
          io.to(nextBidderId).emit('your_turn_to_bid');

          // Also send information about the bidding state to all players
          const biddingStateData = {
            currentBidder: nextBidderId,
            currentBidderName: nextBidder.name,
            currentBidderTeam: nextBidder.team,
            highestBid: lobby.currentBid,
            highestBidder: lobby.highestBidder
          };

          io.to(lobby.lobbyCode).emit('bidding_state_update', biddingStateData);
          if (matchedLobby) io.to(matchedLobby.lobbyCode).emit('bidding_state_update', biddingStateData);

          attempts++;
          break;
        } else {
          // This player's team has already bid, but they're not the highest bidder
          // They still need to explicitly pass
          console.log(`Player ${nextBidder.name}'s team (${nextBidder.team}) has already bid, allowing them to explicitly pass`);
          nextBidderFound = true;

          // Notify next bidder it's their turn
          console.log(`Notifying player ${nextBidder.name} (${nextBidderId}) it's their turn to bid (expected to pass)`);
          io.to(nextBidderId).emit('your_turn_to_bid');

          // Also send information about the bidding state to all players
          const biddingStateData = {
            currentBidder: nextBidderId,
            currentBidderName: nextBidder.name,
            currentBidderTeam: nextBidder.team,
            highestBid: lobby.currentBid,
            highestBidder: lobby.highestBidder
          };

          io.to(lobby.lobbyCode).emit('bidding_state_update', biddingStateData);
          if (matchedLobby) io.to(matchedLobby.lobbyCode).emit('bidding_state_update', biddingStateData);

          attempts++;
          break;
        }
      }

      // This player can bid
      nextBidderFound = true;

      // Notify next bidder it's their turn
      console.log(`Notifying player ${nextBidder.name} (${nextBidderId}) it's their turn to bid`);
      io.to(nextBidderId).emit('your_turn_to_bid');

      // Also send information about the bidding state to all players
      const biddingStateData = {
        currentBidder: nextBidderId,
        currentBidderName: nextBidder.name,
        currentBidderTeam: nextBidder.team,
        highestBid: lobby.currentBid,
        highestBidder: lobby.highestBidder
      };

      io.to(lobby.lobbyCode).emit('bidding_state_update', biddingStateData);
      if (matchedLobby) io.to(matchedLobby.lobbyCode).emit('bidding_state_update', biddingStateData);

      attempts++;
    }

    // If we couldn't find a next bidder, complete bidding
    if (!nextBidderFound) {
      completeBidding(lobby, matchedLobby);
    }
  }

  // Helper function to complete bidding
  function completeBidding(lobby, matchedLobby) {
    console.log('Bidding complete');

    lobby.biddingComplete = true;
    if (matchedLobby) matchedLobby.biddingComplete = true;

    // Get all players from both lobbies
    const allPlayers = [];
    if (lobby.players && Array.isArray(lobby.players)) {
      allPlayers.push(...lobby.players);
    }
    if (matchedLobby && matchedLobby.players && Array.isArray(matchedLobby.players)) {
      allPlayers.push(...matchedLobby.players);
    }

    // If no one bid, use the trump selector that was set earlier (partner of the player who cut the deck)
    if (lobby.highestBidder === null) {
      console.log('No bids placed, checking for pre-determined trump selector');

      // playerPositionUtils is already imported at the top

      // Log all players and their positions for debugging
      console.log("All players in order:");
      allPlayers.forEach((p, idx) => {
        console.log(`Array index ${idx}: ${p.name} (${p.id}) - Team ${p.team} - Position ${p.position}`);
      });

      // Log players by their assigned positions
      console.log("Players by assigned positions:");
      for (let pos = 1; pos <= 4; pos++) {
        const playerAtPosition = allPlayers.find(p => p.position === pos);
        if (playerAtPosition) {
          console.log(`Position ${pos}: ${playerAtPosition.name} (${playerAtPosition.id}) - Team ${playerAtPosition.team}`);
        } else {
          console.log(`Position ${pos}: Not assigned`);
        }
      }

      // Check if a trump selector was already set (partner of the player who cut the deck)
      if (lobby.trumpSelectorId) {
        const trumpSelector = allPlayers.find(p => p.id === lobby.trumpSelectorId);
        if (trumpSelector) {
          console.log(`Using pre-determined trump selector: ${trumpSelector.name} (${trumpSelector.id}) - Team ${trumpSelector.team} - Position ${trumpSelector.position}`);
          lobby.highestBidder = trumpSelector.id;
          if (matchedLobby) matchedLobby.highestBidder = trumpSelector.id;
          console.log(`Default trump selector set to ${trumpSelector.name} (${trumpSelector.id})`);

          // Set the bid to 0 since no one bid
          lobby.currentBid = 0;
          if (matchedLobby) matchedLobby.currentBid = 0;
        } else {
          console.log(`Pre-determined trump selector ${lobby.trumpSelectorId} not found in player list, falling back to position-based approach`);

          // Find the dealer's position
          const dealer = allPlayers.find(p => p.id === lobby.dealerId);
          const dealerPosition = dealer ? dealer.position : 3; // Default to 3 if not found

          // Get the initial trumper position based on the dealer position
          const initialTrumperPosition = playerPositionUtils.getInitialTrumperPosition(dealerPosition);
          console.log(`Looking for player at position ${initialTrumperPosition} (initial trumper for dealer at position ${dealerPosition})`);

          // Find the player with the initial trumper position
          const initialTrumper = allPlayers.find(p => p.position === initialTrumperPosition);

          if (initialTrumper) {
            console.log(`Found initial trumper: ${initialTrumper.name} (${initialTrumper.id}) - Team ${initialTrumper.team} - Position ${initialTrumper.position}`);
            lobby.highestBidder = initialTrumper.id;
            if (matchedLobby) matchedLobby.highestBidder = initialTrumper.id;
            console.log(`Default trump selector set to ${initialTrumper.name} (${initialTrumper.id})`);

            // Set the bid to 0 since no one bid
            lobby.currentBid = 0;
            if (matchedLobby) matchedLobby.currentBid = 0;
          } else {
            console.log(`Could not find player at position ${initialTrumperPosition}, falling back to old method`);

            // Find the dealer's index
            const dealerIndex = allPlayers.findIndex(p => p.id === lobby.dealerId);

            if (dealerIndex !== -1) {
              // The initial trumper is the player to the right of the dealer
              // In a 4-player game with counter-clockwise play, this is the player at index (dealerIndex + 3) % 4
              const initialTrumperIndex = (dealerIndex + 3) % allPlayers.length;
              const fallbackInitialTrumper = allPlayers[initialTrumperIndex];

              console.log(`Dealer index: ${dealerIndex}, Initial trumper index: ${initialTrumperIndex}`);
              console.log(`Fallback initial trumper: ${fallbackInitialTrumper ? fallbackInitialTrumper.name : 'Not found'} (${fallbackInitialTrumper ? fallbackInitialTrumper.id : 'N/A'}) - Team ${fallbackInitialTrumper ? fallbackInitialTrumper.team : 'N/A'}`);

              if (fallbackInitialTrumper) {
                lobby.highestBidder = fallbackInitialTrumper.id;
                if (matchedLobby) matchedLobby.highestBidder = fallbackInitialTrumper.id;
                console.log(`Fallback: Default trump selector set to ${fallbackInitialTrumper.name} (${fallbackInitialTrumper.id})`);

                // Set the bid to 0 since no one bid
                lobby.currentBid = 0;
                if (matchedLobby) matchedLobby.currentBid = 0;
              } else if (lobby.biddingOrder && lobby.biddingOrder.length > 0) {
                // Last resort fallback: use the first player in the bidding order
                lobby.highestBidder = lobby.biddingOrder[0];
                if (matchedLobby) matchedLobby.highestBidder = lobby.biddingOrder[0];
                console.log(`Last resort fallback: Default trump selector set to ${lobby.highestBidder}`);

                // Set the bid to 0 since no one bid
                lobby.currentBid = 0;
                if (matchedLobby) matchedLobby.currentBid = 0;
              }
            }
          }
        }
      } else {
        console.log(`No pre-determined trump selector found, falling back to position-based approach`);

        // Find the dealer's position
        const dealer = allPlayers.find(p => p.id === lobby.dealerId);
        const dealerPosition = dealer ? dealer.position : 3; // Default to 3 if not found

        // Get the initial trumper position based on the dealer position
        const initialTrumperPosition = playerPositionUtils.getInitialTrumperPosition(dealerPosition);
        console.log(`Looking for player at position ${initialTrumperPosition} (initial trumper for dealer at position ${dealerPosition})`);

        // Find the player with the initial trumper position
        const initialTrumper = allPlayers.find(p => p.position === initialTrumperPosition);

        if (initialTrumper) {
          console.log(`Found initial trumper: ${initialTrumper.name} (${initialTrumper.id}) - Team ${initialTrumper.team} - Position ${initialTrumper.position}`);
          lobby.highestBidder = initialTrumper.id;
          if (matchedLobby) matchedLobby.highestBidder = initialTrumper.id;
          console.log(`Default trump selector set to ${initialTrumper.name} (${initialTrumper.id})`);

          // Set the bid to 0 since no one bid
          lobby.currentBid = 0;
          if (matchedLobby) matchedLobby.currentBid = 0;
        } else {
          // Fallback to old method
          console.log(`Could not find player at position ${initialTrumperPosition}, falling back to old method`);

          // Find the dealer's index
          const dealerIndex = allPlayers.findIndex(p => p.id === lobby.dealerId);

          if (dealerIndex !== -1) {
            // The initial trumper is the player to the right of the dealer
            // In a 4-player game with counter-clockwise play, this is the player at index (dealerIndex + 3) % 4
            const initialTrumperIndex = (dealerIndex + 3) % allPlayers.length;
            const fallbackInitialTrumper = allPlayers[initialTrumperIndex];

            console.log(`Dealer index: ${dealerIndex}, Initial trumper index: ${initialTrumperIndex}`);
            console.log(`Fallback initial trumper: ${fallbackInitialTrumper ? fallbackInitialTrumper.name : 'Not found'} (${fallbackInitialTrumper ? fallbackInitialTrumper.id : 'N/A'}) - Team ${fallbackInitialTrumper ? fallbackInitialTrumper.team : 'N/A'}`);

            if (fallbackInitialTrumper) {
              lobby.highestBidder = fallbackInitialTrumper.id;
              if (matchedLobby) matchedLobby.highestBidder = fallbackInitialTrumper.id;
              console.log(`Fallback: Default trump selector set to ${fallbackInitialTrumper.name} (${fallbackInitialTrumper.id})`);

              // Set the bid to 0 since no one bid
              lobby.currentBid = 0;
              if (matchedLobby) matchedLobby.currentBid = 0;
            } else if (lobby.biddingOrder && lobby.biddingOrder.length > 0) {
              // Last resort fallback: use the first player in the bidding order
              lobby.highestBidder = lobby.biddingOrder[0];
              if (matchedLobby) matchedLobby.highestBidder = lobby.biddingOrder[0];
              console.log(`Last resort fallback: Default trump selector set to ${lobby.highestBidder}`);

              // Set the bid to 0 since no one bid
              lobby.currentBid = 0;
              if (matchedLobby) matchedLobby.currentBid = 0;
            }
          }
        }
      }
    }

    // Update trump selector
    if (lobby.highestBidder) {
      lobby.trumpSelectorId = lobby.highestBidder;
      if (matchedLobby) matchedLobby.trumpSelectorId = lobby.highestBidder;

      // Get the trump selector's information
      const trumpSelector = allPlayers.find(p => p.id === lobby.trumpSelectorId);
      const trumpSelectorTeam = trumpSelector ? trumpSelector.team : null;
      const trumpSelectorName = trumpSelector ? trumpSelector.name : "Unknown";

      console.log(`Setting trump selector to ${trumpSelectorName} (${lobby.trumpSelectorId}) from team ${trumpSelectorTeam} with final bid of ${lobby.currentBid}`);

      // Set the trumper team for later use
      if (trumpSelectorTeam) {
        lobby.trumperTeam = trumpSelectorTeam;
        if (matchedLobby) matchedLobby.trumperTeam = trumpSelectorTeam;
        console.log(`Set trumper team to ${trumpSelectorTeam} during bidding completion`);
      }

      // Broadcast bidding complete to all players
      const biddingCompleteData = {
        trumpSelector: lobby.trumpSelectorId,
        trumpSelectorName: trumpSelectorName,
        trumpSelectorTeam: trumpSelectorTeam,
        finalBid: lobby.currentBid,
        isDefaultTrumper: lobby.currentBid === 0 // Indicates if this was a default assignment
      };

      io.to(lobby.lobbyCode).emit('bidding_complete', biddingCompleteData);
      if (matchedLobby) io.to(matchedLobby.lobbyCode).emit('bidding_complete', biddingCompleteData);

      // Notify the trump selector that it's their turn to select trump
      io.to(lobby.trumpSelectorId).emit('your_turn_to_select_trump');
      console.log(`Notified ${trumpSelectorName} (${lobby.trumpSelectorId}) to select trump`);

      // Update game phase
      io.to(lobby.lobbyCode).emit('game_phase_updated', { phase: 'select-trump' });
      if (matchedLobby) io.to(matchedLobby.lobbyCode).emit('game_phase_updated', { phase: 'select-trump' });
    } else {
      console.error('No highest bidder found after bidding complete');
    }
  }

  // Handle trump selection
  socket.on('select_trump', (data, callback) => {
    try {
      console.log('select_trump event received from client:', socket.id, 'data:', data);

      // Check if player is in a game lobby first
      let gameLobbyCode = socketToGameLobby.get(socket.id);
      let lobby, matchedLobby, lobbyCode;

      if (gameLobbyCode && gameLobbies.has(gameLobbyCode)) {
        // Player is in a game lobby
        const gameLobby = gameLobbies.get(gameLobbyCode);
        console.log(`Player ${socket.id} is in game lobby ${gameLobbyCode}`);

        // Use the game lobby for all operations
        lobby = gameLobby;
        matchedLobby = gameLobby; // Same lobby for both
        lobbyCode = gameLobbyCode;
      } else {
        // Fall back to regular lobby
        lobbyCode = socketToLobby.get(socket.id);
        if (!lobbyCode || !lobbies.has(lobbyCode)) {
          console.error('Lobby not found for socket ID:', socket.id);
          return callback?.({ success: false, error: 'Lobby not found' });
        }

        lobby = lobbies.get(lobbyCode);
      }

      // Check if player is the trump selector
      if (lobby.trumpSelectorId !== socket.id) {
        console.error(`Player ${socket.id} is not the trump selector (trump selector is ${lobby.trumpSelectorId})`);
        return callback?.({ success: false, error: 'Only the trump selector can select trump' });
      }

      // If we're not using a game lobby, check if this is a single lobby with all players (invite code case)
      if (!gameLobbyCode) {
        const team1Count = lobby.teams[1]?.length || 0;
        const team2Count = lobby.teams[2]?.length || 0;
        const totalPlayers = team1Count + team2Count;
        const isSingleLobby = totalPlayers === 4;

        if (isSingleLobby) {
          console.log('Single lobby with all 4 players detected (invite code case)');
          // In the invite code case, we use the same lobby
          matchedLobby = lobby;
        } else {
          // In the find match case, we need to get the matched lobby
          if (!lobby.matchedLobby) {
            console.error('No matched lobby found');
            return callback?.({ success: false, error: 'Not matched with another team' });
          }

          matchedLobby = lobbies.get(lobby.matchedLobby);
          if (!matchedLobby) {
            console.error(`Matched lobby ${lobby.matchedLobby} not found`);
            return callback?.({ success: false, error: 'Matched lobby not found' });
          }
        }
      }

      // Get the selected trump suit
      const trumpSuit = data.suit;
      const isLastCard = trumpSuit === 'last_card';

      console.log(`Player ${socket.id} selected ${isLastCard ? 'last card' : trumpSuit} as trump`);

      // Store the trump suit in the lobby data
      lobby.trumpSuit = trumpSuit;
      matchedLobby.trumpSuit = trumpSuit;

      // Notify all players about the trump selection
      io.to(lobbyCode).emit('trump_selected', {
        suit: trumpSuit,
        isLastCard,
        selectedBy: socket.id
      });
      io.to(matchedLobby.lobbyCode).emit('trump_selected', {
        suit: trumpSuit,
        isLastCard,
        selectedBy: socket.id
      });

      // After trump selection, the dealer needs to deal 2 more cards to each player
      // Then the player to the right of the trump selector plays first

      // Get the dealer ID
      const dealerId = lobby.dealerId;

      // Get the trump selector ID
      const trumpSelectorId = lobby.trumpSelectorId;

      // Store the trump selection in the game state
      lobby.gameState = lobby.gameState || {};
      matchedLobby.gameState = matchedLobby.gameState || {};

      lobby.gameState.trumpSuit = trumpSuit;
      lobby.gameState.isLastCard = isLastCard;
      matchedLobby.gameState.trumpSuit = trumpSuit;
      matchedLobby.gameState.isLastCard = isLastCard;

      // Get the trump selector's team
      const trumpSelectorPlayer = allPlayers.find(p => p.id === trumpSelectorId);
      if (trumpSelectorPlayer) {
        // Store the trumper team for Jordhi validation
        lobby.trumperTeam = trumpSelectorPlayer.team;
        matchedLobby.trumperTeam = trumpSelectorPlayer.team;
        console.log(`Set trumper team to ${trumpSelectorPlayer.team}`);
      }

      // Notify the dealer to deal the final 2 cards
      console.log(`Sending deal_final_cards_request to dealer ${dealerId}`);
      io.to(dealerId).emit('deal_final_cards_request');

      // Get all players from both lobbies
      const allPlayers = [];

      // Check if this is a single lobby with all players (invite code case)
      const team1Count = lobby.teams[1]?.length || 0;
      const team2Count = lobby.teams[2]?.length || 0;
      const totalPlayers = team1Count + team2Count;
      const isSingleLobby = totalPlayers === 4;

      // Check if teams structure exists
      if (lobby.teams && matchedLobby.teams) {
        // Use teams structure
        for (const teamId of [1, 2]) {
          if (lobby.teams[teamId]) {
            lobby.teams[teamId].forEach(player => {
              // Only add each player once by checking if they're already in the array
              if (!allPlayers.some(p => p.id === player.id)) {
                allPlayers.push(player);
              }
            });
          }

          // Only add players from matched lobby if it's different from the main lobby
          if (!isSingleLobby && matchedLobby.teams[teamId]) {
            matchedLobby.teams[teamId].forEach(player => {
              // Only add each player once by checking if they're already in the array
              if (!allPlayers.some(p => p.id === player.id)) {
                allPlayers.push(player);
              }
            });
          }
        }
      } else {
        // Fallback to players array
        if (Array.isArray(lobby.players)) {
          lobby.players.forEach(player => {
            if (!allPlayers.some(p => p.id === player.id)) {
              allPlayers.push(player);
            }
          });
        }

        // Only add players from matched lobby if it's different from the main lobby
        if (!isSingleLobby && Array.isArray(matchedLobby.players)) {
          matchedLobby.players.forEach(player => {
            if (!allPlayers.some(p => p.id === player.id)) {
              allPlayers.push(player);
            }
          });
        }
      }

      // Ensure we have exactly 4 players
      if (allPlayers.length !== 4) {
        console.error(`Expected 4 players but got ${allPlayers.length}:`, allPlayers.map(p => `${p.name} (${p.id}) - Team ${p.team} - Position ${p.position}`));
      }

      console.log('All players for next player determination:', allPlayers.map(p => `${p.name} (${p.id}) - Team ${p.team}`));

      // Store the next player (to the right of trump selector) who will play first
      // According to Thunee rules, the player to the right of the trumper plays first
      const trumpSelector = allPlayers.find(p => p.id === trumpSelectorId);
      if (!trumpSelector) {
        console.error(`Trump selector ${trumpSelectorId} not found in player list`);
        return callback?.({ success: false, error: 'Trump selector not found' });
      }

      console.log(`Trump selector found: ${trumpSelector.name} (${trumpSelector.id}) - Team ${trumpSelector.team}`);

      // Log all players and their positions for debugging
      console.log("All players in order:");
      allPlayers.forEach((p, idx) => {
        console.log(`Position ${idx}: ${p.name} (${p.id}) - Team ${p.team}`);
      });

      // playerPositionUtils is already imported at the top

      // Find the trumper's position
      const trumperPosition = trumpSelector.position;
      console.log(`Trumper position: ${trumperPosition}`);

      // According to the specific rule set:
      // - If Player 1 selects trump → Player 3 plays first
      // - If Player 2 selects trump → Player 4 plays first
      // - If Player 3 selects trump → Player 2 plays first
      // - If Player 4 selects trump → Player 1 plays first
      const firstPlayerPosition = playerPositionUtils.getFirstPlayerAfterTrump(trumperPosition);
      console.log(`First player position after trump selection: ${firstPlayerPosition}`);

      // Find the player with the first player position
      const firstPlayer = allPlayers.find(p => p.position === firstPlayerPosition);

      // Log the first player after trump selection
      console.log(`First player after trump selection: ${firstPlayer ? firstPlayer.name : 'Not found'} (${firstPlayer ? firstPlayer.id : 'N/A'}) - Team ${firstPlayer ? firstPlayer.team : 'N/A'} - Position ${firstPlayer ? firstPlayer.position : 'N/A'}`);

      console.log(`Trump selector ${trumpSelector.name} is at position ${trumperPosition}, first player is at position ${firstPlayerPosition}`);

      let playerToGoFirst = firstPlayer;

      // If the first player is not found, use a fallback
      if (!firstPlayer) {
        console.error(`Could not find player at position ${firstPlayerPosition}`);

        // Fallback: find any player who is not the dealer and not the trumper
        const fallbackPlayer = allPlayers.find(p => p.id !== lobby.dealerId && p.id !== trumpSelector.id);
        if (fallbackPlayer) {
          console.log(`Fallback: Using player ${fallbackPlayer.name} (${fallbackPlayer.id}) as first player`);
          playerToGoFirst = fallbackPlayer;
        }
      }

      if (!playerToGoFirst) {
        console.error(`Could not find player to the right of trump selector`);
        // Fallback to old method
        const trumpSelectorTeam = trumpSelector.team;
        const oppositeTeam = trumpSelectorTeam === 1 ? 2 : 1;
        console.log(`Falling back to finding player from opposite team ${oppositeTeam}`);
        const fallbackNextPlayer = allPlayers.find(p => p.team === oppositeTeam);
        if (fallbackNextPlayer) {
          console.log(`Found fallback next player: ${fallbackNextPlayer.name}`);
          playerToGoFirst = fallbackNextPlayer;
        }
      }

      if (playerToGoFirst) {
        console.log(`Setting next player to ${playerToGoFirst.id} (${playerToGoFirst.name})`);
        lobby.gameState.nextPlayerId = playerToGoFirst.id;
        if (matchedLobby) matchedLobby.gameState.nextPlayerId = playerToGoFirst.id;

        // Send an explicit player_turn event to notify all clients who goes first
        // This is important for the UI to show the correct player's turn
        console.log(`Sending explicit player_turn event for first player: ${playerToGoFirst.name} (${playerToGoFirst.id})`);
        io.to(lobbyCode).emit('first_player_determined', {
          playerId: playerToGoFirst.id,
          playerName: playerToGoFirst.name,
          playerTeam: playerToGoFirst.team,
          message: "This player will play first after the final cards are dealt"
        });
        if (matchedLobby) {
          io.to(matchedLobby.lobbyCode).emit('first_player_determined', {
            playerId: playerToGoFirst.id,
            playerName: playerToGoFirst.name,
            playerTeam: playerToGoFirst.team,
            message: "This player will play first after the final cards are dealt"
          });
        }
      }

      callback?.({ success: true });
    } catch (error) {
      console.error('Error in select_trump:', error);
      callback?.({ success: false, error: error.message });
    }
  });

  // Handle deal final cards event
  socket.on('deal_final_cards', (_, callback) => {
    try {
      console.log('deal_final_cards event received from client:', socket.id);

      // Get lobby code
      const lobbyCode = socketToLobby.get(socket.id);
      if (!lobbyCode || !lobbies.has(lobbyCode)) {
        console.error('Lobby not found for socket ID:', socket.id);
        return callback?.({ success: false, error: 'Lobby not found' });
      }

      const lobby = lobbies.get(lobbyCode);
      console.log(`Processing deal_final_cards in lobby ${lobbyCode}, dealer ID: ${lobby.dealerId}`);

      // Check if player is the dealer
      if (lobby.dealerId !== socket.id) {
        console.error(`Player ${socket.id} is not the dealer (dealer is ${lobby.dealerId})`);
        return callback?.({ success: false, error: 'Only the dealer can deal cards' });
      }

      // Check if this is a single lobby with all players (invite code case)
      const team1Count = lobby.teams[1]?.length || 0;
      const team2Count = lobby.teams[2]?.length || 0;
      const totalPlayers = team1Count + team2Count;
      const isSingleLobby = totalPlayers === 4;

      let matchedLobby;

      if (isSingleLobby) {
        console.log('Single lobby with all 4 players detected (invite code case)');
        // In the invite code case, we use the same lobby
        matchedLobby = lobby;
      } else {
        // In the find match case, we need to get the matched lobby
        if (!lobby.matchedLobby) {
          console.error('No matched lobby found');
          return callback?.({ success: false, error: 'Not matched with another team' });
        }

        matchedLobby = lobbies.get(lobby.matchedLobby);
        if (!matchedLobby) {
          console.error(`Matched lobby ${lobby.matchedLobby} not found`);
          return callback?.({ success: false, error: 'Matched lobby not found' });
        }
      }

      // Deal 2 more cards to each player
      console.log('Dealing final 2 cards to players in lobby:', lobbyCode);

      // Get all players from both lobbies
      const allPlayers = [];

      // We already have isSingleLobby from earlier in the function

      // Add players from both teams in both lobbies
      for (const teamId of [1, 2]) {
        if (lobby.teams[teamId]) {
          lobby.teams[teamId].forEach(player => {
            // Only add each player once by checking if they're already in the array
            if (!allPlayers.some(p => p.id === player.id)) {
              allPlayers.push(player);
            }
          });
        }

        // Only add players from matched lobby if it's different from the main lobby
        if (!isSingleLobby && matchedLobby.teams[teamId]) {
          matchedLobby.teams[teamId].forEach(player => {
            // Only add each player once by checking if they're already in the array
            if (!allPlayers.some(p => p.id === player.id)) {
              allPlayers.push(player);
            }
          });
        }
      }

      // Ensure we have exactly 4 players
      if (allPlayers.length !== 4) {
        console.error(`Expected 4 players but got ${allPlayers.length}:`, allPlayers.map(p => `${p.name} (${p.id}) - Team ${p.team} - Position ${p.position}`));
      }

      console.log(`Found ${allPlayers.length} players total for final card dealing`);

      // Create a new deck for the final 2 cards if needed
      if (!lobby.gameDeck || lobby.gameDeck.length < 8) { // Need at least 8 cards (2 per player)
        console.log('Creating new deck for final cards');
        lobby.gameDeck = cardUtils.createGameDeck();
        matchedLobby.gameDeck = lobby.gameDeck;
      }

      // Deal the cards with a delay
      setTimeout(() => {
        // Use our specialized final card dealer
        console.log('Using finalCardDealer to deal the final 2 cards');
        finalCardDealer.dealFinalCards(io, lobby, matchedLobby);

        // Wait a bit to ensure all cards are dealt before proceeding to gameplay
        setTimeout(() => {
          console.log('All final cards have been dealt, proceeding to gameplay phase');

          // Get all players from both lobbies
          const allPlayers = [];

          // Add players from both teams in both lobbies
          for (const teamId of [1, 2]) {
            if (lobby.teams[teamId]) {
              lobby.teams[teamId].forEach(player => {
                // Only add each player once by checking if they're already in the array
                if (!allPlayers.some(p => p.id === player.id)) {
                  allPlayers.push(player);
                }
              });
            }

            // Only add players from matched lobby if it's different from the main lobby
            if (!isSingleLobby && matchedLobby.teams[teamId]) {
              matchedLobby.teams[teamId].forEach(player => {
                // Only add each player once by checking if they're already in the array
                if (!allPlayers.some(p => p.id === player.id)) {
                  allPlayers.push(player);
                }
              });
            }
          }

          // Ensure we have exactly 4 players
          if (allPlayers.length !== 4) {
            console.error(`Expected 4 players but got ${allPlayers.length}:`, allPlayers.map(p => `${p.name} (${p.id}) - Team ${p.team} - Position ${p.position}`));
          }

          console.log('All players for game start:', allPlayers.map(p => `${p.name} (${p.id}) - Team ${p.team}`));

          // Get the next player ID from the game state
          let nextPlayerId = lobby.gameState?.nextPlayerId;

          // The first player should be the person to the right of the trumper
          if (lobby.trumpSelectorId) {
            const trumpSelector = allPlayers.find(p => p.id === lobby.trumpSelectorId);
            if (trumpSelector) {
              console.log(`Trump selector found: ${trumpSelector.name} (${trumpSelector.id}) - Team ${trumpSelector.team}`);

              // Log all players and their positions for debugging
              console.log("All players in order:");
              allPlayers.forEach((p, idx) => {
                console.log(`Position ${idx}: ${p.name} (${p.id}) - Team ${p.team}`);
              });

              // Import the playerPositionUtils
              const playerPositionUtils = require('./src/utils/server/playerPositionUtils');

              // Find the trumper's position
              const trumperPosition = trumpSelector.position;
              console.log(`Trumper position: ${trumperPosition}`);

              // According to the specific rule set:
              // - If Player 1 selects trump → Player 3 plays first
              // - If Player 2 selects trump → Player 4 plays first
              // - If Player 3 selects trump → Player 2 plays first
              // - If Player 4 selects trump → Player 1 plays first
              const firstPlayerPosition = playerPositionUtils.getFirstPlayerAfterTrump(trumperPosition);
              console.log(`First player position after trump selection: ${firstPlayerPosition}`);

              // Find the player with the first player position
              const firstPlayer = allPlayers.find(p => p.position === firstPlayerPosition);

              // Log the first player after trump selection
              console.log(`First player after trump selection: ${firstPlayer ? firstPlayer.name : 'Not found'} (${firstPlayer ? firstPlayer.id : 'N/A'}) - Team ${firstPlayer ? firstPlayer.team : 'N/A'} - Position ${firstPlayer ? firstPlayer.position : 'N/A'}`);

              console.log(`Trump selector ${trumpSelector.name} is at position ${trumperPosition}, first player is at position ${firstPlayerPosition}`);

              let playerToGoFirst = firstPlayer;

              // If the first player is not found, use a fallback
              if (!firstPlayer) {
                console.error(`Could not find player at position ${firstPlayerPosition}`);

                // Fallback: find any player who is not the dealer and not the trumper
                const fallbackPlayer = allPlayers.find(p => p.id !== lobby.dealerId && p.id !== trumpSelector.id);
                if (fallbackPlayer) {
                  console.log(`Fallback: Using player ${fallbackPlayer.name} (${fallbackPlayer.id}) as first player`);
                  playerToGoFirst = fallbackPlayer;
                }
              }

              if (playerToGoFirst) {
                nextPlayerId = playerToGoFirst.id;
                console.log(`Setting next player to ${playerToGoFirst.name} (${nextPlayerId}) - player to the right of trumper`);

                // Store this in the game state for consistency
                lobby.gameState.nextPlayerId = nextPlayerId;
                if (matchedLobby) matchedLobby.gameState.nextPlayerId = nextPlayerId;

                // Make sure the player order is correct for the first turn
                console.log(`First player to play: ${playerToGoFirst.name} (${nextPlayerId}) - Team ${playerToGoFirst.team}`);
              } else {
                console.error(`Could not find player to go first`);
              }
            } else {
              console.error(`Trump selector with ID ${lobby.trumpSelectorId} not found in players list`);
            }
          } else {
            console.error("No trump selector ID found in lobby");
          }

          // If still no next player, choose a random player
          if (!nextPlayerId && allPlayers.length > 0) {
            const randomPlayer = allPlayers[Math.floor(Math.random() * allPlayers.length)];
            nextPlayerId = randomPlayer.id;
            console.log(`FALLBACK: Choosing random player ${randomPlayer.name} (${nextPlayerId}) to go first`);
          }

          // Notify all players that the final cards have been dealt and gameplay can begin
          io.to(lobbyCode).emit('final_cards_dealt', {
            nextPlayerId,
            trumpSuit: lobby.gameState?.trumpSuit,
            isLastCard: lobby.gameState?.isLastCard
          });
          io.to(matchedLobby.lobbyCode).emit('final_cards_dealt', {
            nextPlayerId,
            trumpSuit: lobby.gameState?.trumpSuit,
            isLastCard: lobby.gameState?.isLastCard
          });

          // Explicitly send a player_turn event to set the first player's turn
          if (nextPlayerId) {
            console.log(`Sending explicit player_turn event for player ${nextPlayerId}`);
            io.to(lobbyCode).emit('player_turn', { playerId: nextPlayerId });
            io.to(matchedLobby.lobbyCode).emit('player_turn', { playerId: nextPlayerId });
          } else {
            console.error('No nextPlayerId found! Cannot set player turn.');
          }
        }, 3000); // Wait 3 seconds after dealing cards
      }, 1000);

      callback?.({ success: true });
    } catch (error) {
      console.error('Error in deal_final_cards:', error);
      callback?.({ success: false, error: error.message });
    }
  });

  // Original deal_cards handler
  socket.on('deal_cards', (_, callback) => {
    try {
      console.log('deal_cards event received from client:', socket.id);

      // Get lobby code
      const lobbyCode = socketToLobby.get(socket.id);
      if (!lobbyCode || !lobbies.has(lobbyCode)) {
        console.error('Lobby not found for socket ID:', socket.id);
        return callback?.({ success: false, error: 'Lobby not found' });
      }

      const lobby = lobbies.get(lobbyCode);
      console.log(`Processing deal_cards in lobby ${lobbyCode}, dealer ID: ${lobby.dealerId}`);

      // Check if player is the dealer
      if (lobby.dealerId !== socket.id) {
        console.error(`Player ${socket.id} is not the dealer (dealer is ${lobby.dealerId})`);
        return callback?.({ success: false, error: 'Only the dealer can deal cards' });
      }

      // Check if this is a single lobby with all players (invite code case)
      const team1Count = lobby.teams[1]?.length || 0;
      const team2Count = lobby.teams[2]?.length || 0;
      const totalPlayers = team1Count + team2Count;
      const isSingleLobby = totalPlayers === 4;

      let matchedLobby;

      if (isSingleLobby) {
        console.log('Single lobby with all 4 players detected (invite code case)');
        // In the invite code case, we use the same lobby
        matchedLobby = lobby;
      } else {
        // In the find match case, we need to get the matched lobby
        if (!lobby.matchedLobby) {
          console.error('No matched lobby found');
          return callback?.({ success: false, error: 'Not matched with another team' });
        }

        matchedLobby = lobbies.get(lobby.matchedLobby);
        if (!matchedLobby) {
          console.error(`Matched lobby ${lobby.matchedLobby} not found`);
          return callback?.({ success: false, error: 'Matched lobby not found' });
        }
      }

      console.log(`Dealer ${socket.id} is dealing cards in lobby ${lobbyCode}, matched with ${matchedLobby.lobbyCode}`);

      // Create a shuffled deck for the game
      const gameDeck = createGameDeck();
      console.log(`Created game deck with ${gameDeck.length} cards`);

      // Get all players from both lobbies
      let allPlayers = [];

      // Add players from both lobbies
      if (lobby.teams[1] && lobby.teams[1].length > 0) {
        allPlayers = [...allPlayers, ...lobby.teams[1]];
      }
      if (lobby.teams[2] && lobby.teams[2].length > 0) {
        allPlayers = [...allPlayers, ...lobby.teams[2]];
      }
      if (matchedLobby.teams[1] && matchedLobby.teams[1].length > 0) {
        allPlayers = [...allPlayers, ...matchedLobby.teams[1]];
      }
      if (matchedLobby.teams[2] && matchedLobby.teams[2].length > 0) {
        allPlayers = [...allPlayers, ...matchedLobby.teams[2]];
      }

      console.log('Players before filtering:', allPlayers.map(p => `${p.name} (${p.id}) - Team ${p.team}`));

      // Ensure we have exactly 4 players
      if (allPlayers.length !== 4) {
        console.error(`Expected 4 players but got ${allPlayers.length}`);
        return callback?.({ success: false, error: 'Need exactly 4 players to deal cards' });
      }

      // Sort players to ensure consistent order
      allPlayers = allPlayers.sort((a, b) => a.id.localeCompare(b.id));
      console.log('Players after sorting:', allPlayers.map(p => `${p.name} (${p.id}) - Team ${p.team}`));

      // Find the dealer's index in the sorted player array
      const dealerIndex = allPlayers.findIndex(p => p.id === socket.id);
      if (dealerIndex === -1) {
        console.error('Dealer not found in player list');
        return callback?.({ success: false, error: 'Dealer not found in player list' });
      }
      console.log(`Dealer index in sorted players: ${dealerIndex}`);

      // Rearrange players so that dealing starts with the player to the right of the dealer
      // In anti-clockwise order, that's the player at index dealerIndex - 1
      let startPlayerIndex = dealerIndex - 1;
      if (startPlayerIndex < 0) startPlayerIndex = allPlayers.length - 1;
      console.log(`Starting deal with player index ${startPlayerIndex}: ${allPlayers[startPlayerIndex].name}`);

      // Start dealing cards one at a time in anti-clockwise order
      console.log('Starting dealCardsOneAtATime function');
      dealCardsOneAtATime(lobby, matchedLobby, allPlayers, gameDeck, 0, startPlayerIndex, {});

      console.log('deal_cards event processed successfully');
      callback?.({ success: true });
    } catch (error) {
      console.error('Error handling deal cards:', error);
      callback?.({ success: false, error: error.message });
    }
  });

  // Removed duplicate select_trump handler

  socket.on('play_card', (data, callback) => {
    try {
      // Check if player is in a game lobby first
      let gameLobbyCode = socketToGameLobby.get(socket.id);
      let lobby, matchedLobby, lobbyCode;

      // Stop the turn timer since the player has played a card
      // We'll get the actual lobby object below

      if (gameLobbyCode && gameLobbies.has(gameLobbyCode)) {
        // Player is in a game lobby
        const gameLobby = gameLobbies.get(gameLobbyCode);
        console.log(`Player ${socket.id} is in game lobby ${gameLobbyCode}`);

        // Use the game lobby for all operations
        lobby = gameLobby;
        matchedLobby = gameLobby; // Same lobby for both
        lobbyCode = gameLobbyCode;

        // Stop the turn timer
        turnUtils.stopTurnTimer(lobby);
      } else {
        // Fall back to regular lobby
        lobbyCode = socketToLobby.get(socket.id);
        if (!lobbyCode || !lobbies.has(lobbyCode)) {
          console.error('Lobby not found for socket ID:', socket.id);
          return callback?.({ success: false, error: 'Lobby not found' });
        }

        lobby = lobbies.get(lobbyCode);

        // Stop the turn timer
        turnUtils.stopTurnTimer(lobby);

        // If we're not using a game lobby, check if this is a single lobby with all players (invite code case)
        const team1Count = lobby.teams[1]?.length || 0;
        const team2Count = lobby.teams[2]?.length || 0;
        const totalPlayers = team1Count + team2Count;
        const isSingleLobby = totalPlayers === 4;

        if (isSingleLobby) {
          console.log('Single lobby with all 4 players detected (invite code case)');
          // In the invite code case, we use the same lobby
          matchedLobby = lobby;
        } else {
          // In the find match case, we need to get the matched lobby
          if (!lobby.matchedLobby) {
            console.error('No matched lobby found');
            return callback?.({ success: false, error: 'Not matched with another team' });
          }

          matchedLobby = lobbies.get(lobby.matchedLobby);
          if (!matchedLobby) {
            console.error(`Matched lobby ${lobby.matchedLobby} not found`);
            return callback?.({ success: false, error: 'Matched lobby not found' });
          }
        }
      }

      // Make sure the card has the player ID attached
      const cardWithPlayer = {
        ...data.card,
        playedBy: socket.id
      };

      // Remove the card from the player's hand
      if (lobby.playerCards && lobby.playerCards[socket.id]) {
        // Find the card in the player's hand by ID
        const cardIndex = lobby.playerCards[socket.id].findIndex(c => c.id === data.card.id);
        if (cardIndex !== -1) {
          // Remove the card from the player's hand
          lobby.playerCards[socket.id].splice(cardIndex, 1);
          console.log(`Removed card ${data.card.value} of ${data.card.suit} from player ${socket.id}'s hand`);
        } else {
          console.warn(`Card ${data.card.value} of ${data.card.suit} not found in player ${socket.id}'s hand`);
        }
      } else {
        console.warn(`No player cards found for player ${socket.id}`);
      }

      // Do the same for the matched lobby
      if (matchedLobby.playerCards && matchedLobby.playerCards[socket.id]) {
        const cardIndex = matchedLobby.playerCards[socket.id].findIndex(c => c.id === data.card.id);
        if (cardIndex !== -1) {
          matchedLobby.playerCards[socket.id].splice(cardIndex, 1);
        }
      }

      console.log(`Player ${socket.id} played card:`, cardWithPlayer);

      // Get all players from both lobbies
      const allPlayers = [];

      // Check if this is a single lobby with all players (invite code case)
      const team1Count = lobby.teams[1]?.length || 0;
      const team2Count = lobby.teams[2]?.length || 0;
      const totalPlayers = team1Count + team2Count;
      const isSingleLobby = totalPlayers === 4;

      // Check if teams structure exists
      if (lobby.teams && matchedLobby.teams) {
        // Use teams structure
        for (const teamId of [1, 2]) {
          if (lobby.teams[teamId]) {
            lobby.teams[teamId].forEach(player => {
              // Only add each player once by checking if they're already in the array
              if (!allPlayers.some(p => p.id === player.id)) {
                allPlayers.push(player);
              }
            });
          }

          // Only add players from matched lobby if it's different from the main lobby
          if (!isSingleLobby && matchedLobby.teams[teamId]) {
            matchedLobby.teams[teamId].forEach(player => {
              // Only add each player once by checking if they're already in the array
              if (!allPlayers.some(p => p.id === player.id)) {
                allPlayers.push(player);
              }
            });
          }
        }
      } else {
        // Fallback to players array
        if (Array.isArray(lobby.players)) {
          lobby.players.forEach(player => {
            if (!allPlayers.some(p => p.id === player.id)) {
              allPlayers.push(player);
            }
          });
        }

        // Only add players from matched lobby if it's different from the main lobby
        if (!isSingleLobby && Array.isArray(matchedLobby.players)) {
          matchedLobby.players.forEach(player => {
            if (!allPlayers.some(p => p.id === player.id)) {
              allPlayers.push(player);
            }
          });
        }
      }

      // Ensure we have exactly 4 players
      if (allPlayers.length !== 4) {
        console.error(`Expected 4 players but got ${allPlayers.length}:`, allPlayers.map(p => `${p.name} (${p.id}) - Team ${p.team} - Position ${p.position}`));
      }

      // Get player info for logging
      const playerInfo = allPlayers.find(p => p.id === socket.id);
      const playerName = playerInfo?.name || 'Unknown';
      const playerTeam = playerInfo?.team || 'Unknown';
      console.log(`Player ${playerName} (Team ${playerTeam}) played ${cardWithPlayer.value} of ${cardWithPlayer.suit}`);

      // Forward action to all players in both lobbies with the player ID
      // Make sure to include complete player information for proper rendering
      const playCardData = {
        card: cardWithPlayer,
        playerId: socket.id,
        playerInfo: {
          name: playerName,
          team: playerTeam,
          position: playerInfo?.position // Include position in playerInfo
        },
        allPlayers: allPlayers.map(p => ({
          id: p.id,
          name: p.name,
          team: p.team,
          position: p.position // Include position information
        }))
      };

      // Log the player positions for debugging
      console.log("Player positions in playCardData:");
      playCardData.allPlayers.forEach(p => {
        console.log(`Player: ${p.name} (${p.id}) - Team ${p.team} - Position ${p.position}`);
      });

      // Initialize or update the played cards array for this hand
      if (!lobby.currentHandCards) {
        lobby.currentHandCards = [];

        // Only initialize matchedLobby if it's different from the main lobby
        if (matchedLobby !== lobby && !matchedLobby.currentHandCards) {
          matchedLobby.currentHandCards = [];
        }
      }

      // Store the player's hand before playing for 4-ball validation
      if (lobby.playerCards && lobby.playerCards[socket.id]) {
        // Create a deep copy of the player's current hand
        const handBeforePlay = JSON.parse(JSON.stringify(lobby.playerCards[socket.id]));

        // Add the handBeforePlay to the card data
        cardWithPlayer.handBeforePlay = handBeforePlay;

        console.log(`Stored player ${socket.id}'s hand (${handBeforePlay.length} cards) before playing for 4-ball validation`);
      }

      // Add the card to the current hand
      lobby.currentHandCards.push(cardWithPlayer);

      // Only add to matchedLobby if it's different from the main lobby
      if (matchedLobby !== lobby) {
        matchedLobby.currentHandCards.push(cardWithPlayer);
      }

      // Send to both lobbies to ensure all players see the card
      io.to(lobbyCode).emit('card_played', playCardData);
      if (matchedLobby.lobbyCode && matchedLobby.lobbyCode !== lobbyCode) {
        io.to(matchedLobby.lobbyCode).emit('card_played', playCardData);
      }

      // Sort players to ensure consistent order
      allPlayers.sort((a, b) => a.id.localeCompare(b.id));

      // Find the current player's index
      const currentPlayerIndex = allPlayers.findIndex(p => p.id === socket.id);
      if (currentPlayerIndex === -1) {
        console.error(`Player ${socket.id} not found in player list`);
        return callback?.({ success: false, error: 'Player not found' });
      }

      // Get the unique player IDs who have played cards
      const uniquePlayerIds = new Set(lobby.currentHandCards.map(card => card.playedBy));
      console.log(`Current hand has ${lobby.currentHandCards.length} cards played by ${uniquePlayerIds.size} unique players`);

      // Check if all 4 players have played a card
      if (uniquePlayerIds.size === 4) {
        console.log('All 4 players have played a card. Determining winner...');

        // If this will be the 6th hand, set the ball completion flag early to prevent timeouts
        if (lobby.currentHandId >= 5) { // currentHandId is 0-based, so 5 means this is the 6th hand
          lobby.ballCompleted = true;
          matchedLobby.ballCompleted = true;
          console.log('Ball completion flag set early to prevent timeout race conditions');
        }

        // Get the trump suit
        const trumpSuit = lobby.gameState?.trumpSuit || 'hearts'; // Default to hearts if not set

        // Check if there's a Thunee player
        const thuneePlayerId = lobby.thuneePlayer || null;

        // Determine the winner of the hand
        const handResult = gameUtils.determineHandWinner(lobby.currentHandCards, trumpSuit, thuneePlayerId);

        if (!handResult) {
          console.error('Failed to determine hand winner');
          return callback?.({ success: false, error: 'Failed to determine hand winner' });
        }

        const { winningCard, winningPlayerId, points, winReason, isThuneeFailure, trumpSuit: updatedTrumpSuit } = handResult;

        // If this is the first hand after a Thunee call, update the trump suit
        if (thuneePlayerId && updatedTrumpSuit !== trumpSuit) {
          console.log(`Updating trump suit from ${trumpSuit} to ${updatedTrumpSuit} based on Thunee call`);
          lobby.gameState.trumpSuit = updatedTrumpSuit;
          matchedLobby.gameState.trumpSuit = updatedTrumpSuit;

          // Notify all players of the trump suit change
          io.to(lobbyCode).emit('trump_updated', { trumpSuit: updatedTrumpSuit });
          io.to(matchedLobby.lobbyCode).emit('trump_updated', { trumpSuit: updatedTrumpSuit });
        }

        // Check if this is a Thunee failure
        if (isThuneeFailure) {
          // Find the Thunee player
          const thuneePlayer = allPlayers.find(p => p.id === thuneePlayerId);
          if (!thuneePlayer) {
            console.error(`Thunee player ${thuneePlayerId} not found in player list`);
          } else {
            // Find the winning player
            const winningPlayer = allPlayers.find(p => p.id === winningPlayerId);
            if (!winningPlayer) {
              console.error(`Winning player ${winningPlayerId} not found in player list`);
            } else {
              // Check if the winner is the Thunee caller's partner
              const isPartner = thuneePlayer.team === winningPlayer.team;

              console.log(`Thunee failure: ${thuneePlayer.name} (Team ${thuneePlayer.team}) called Thunee but ${winningPlayer.name} (Team ${winningPlayer.team}) won the hand`);

              // Determine how many balls to award
              let ballsToAward = 0;
              let winningTeam = 0;

              if (isPartner) {
                // If partner caught the Thunee caller, opposing team gets 8 balls
                ballsToAward = 8;
                winningTeam = thuneePlayer.team === 1 ? 2 : 1;
                console.log(`Partner caught Thunee caller! Team ${winningTeam} awarded ${ballsToAward} balls`);
              } else {
                // If opposing team caught the Thunee caller, they get 4 balls
                ballsToAward = 4;
                winningTeam = winningPlayer.team;
                console.log(`Opposing team caught Thunee caller! Team ${winningTeam} awarded ${ballsToAward} balls`);
              }

              // Update the ball scores
              if (!lobby.ballScores) {
                lobby.ballScores = { team1: 0, team2: 0 };
                matchedLobby.ballScores = { team1: 0, team2: 0 };
              }

              lobby.ballScores[`team${winningTeam}`] += ballsToAward;
              matchedLobby.ballScores[`team${winningTeam}`] += ballsToAward;

              // Prepare comprehensive Thunee failure data
              const thuneeFailureData = {
                thuneePlayerId,
                thuneePlayerName: thuneePlayer.name,
                thuneePlayerTeam: thuneePlayer.team,
                winningPlayerId,
                winningPlayerName: winningPlayer.name,
                winningPlayerTeam: winningPlayer.team,
                isPartner,
                ballsAwarded: ballsToAward,
                winningTeam,
                ballScores: lobby.ballScores,
                // Add comprehensive hand history
                handsPlayed: lobby.hands || [],
                currentHand: {
                  id: lobby.currentHandId,
                  cards: lobby.currentHandCards,
                  winner: winningPlayer,
                  winningCard: winningCard,
                  winReason: winReason
                },
                caughtInHand: lobby.currentHandId + 1, // 1-based hand number
                caughtByCard: winningCard,
                instantResult: true,
                trumpSuit: lobby.gameState?.trumpSuit
              };

              // Notify all players of the Thunee failure
              io.to(lobbyCode).emit('thunee_failure', thuneeFailureData);

              // Check if game should end after Thunee failure
              const gameEndUtils = require('./src/utils/server/gameEndUtils');
              const thuneeFailureGameEndCheck = gameEndUtils.checkGameEnd(lobby);
              if (thuneeFailureGameEndCheck.gameEnded) {
                console.log(`Game ended after Thunee failure! Team ${thuneeFailureGameEndCheck.winner} wins!`);

                const gameHistory = gameEndUtils.getGameHistory(lobby);
                const gameEndData = {
                  ...thuneeFailureGameEndCheck,
                  gameHistory
                };

                // Emit game_ended event to both lobbies
                io.to(lobbyCode).emit('game_ended', gameEndData);
                io.to(matchedLobby.lobbyCode).emit('game_ended', gameEndData);

                return; // Don't continue with normal hand processing
              }

              if (matchedLobby.lobbyCode !== lobbyCode) {
                io.to(matchedLobby.lobbyCode).emit('thunee_failure', thuneeFailureData);
              }

              // Clear the Thunee player since the Thunee attempt failed
              lobby.thuneePlayer = null;
              matchedLobby.thuneePlayer = null;

              // Start a new ball with a complete reset of card tracking
              console.log("Thunee call failed - performing complete card tracking reset");
              resetUtils.resetBallState(lobby, matchedLobby);

              // Explicitly clear all card tracking
              lobby.playerCards = {};
              matchedLobby.playerCards = {};
              lobby.allDealtCards = new Set();
              matchedLobby.allDealtCards = new Set();
              lobby.cardsDealtPerPlayer = {};
              matchedLobby.cardsDealtPerPlayer = {};

              // Reset hand counter and points
              lobby.currentHandId = 0;
              matchedLobby.currentHandId = 0;
              lobby.ballPoints = { team1: 0, team2: 0 };
              matchedLobby.ballPoints = { team1: 0, team2: 0 };

              // Get the current ball ID
              const ballId = (lobby.currentBallId || 0) + 1;
              lobby.currentBallId = ballId;
              matchedLobby.currentBallId = ballId;

              // Send ball_completed event to both lobbies
              const ballCompletedData = {
                ballId,
                winner: winningTeam,
                points: {
                  team1: 0,
                  team2: 0
                },
                nextDealer: lobby.dealerId, // Keep the same dealer
                ballScores: lobby.ballScores,
                thuneeFailure: true,
                ballsAwarded: ballsToAward,
                instantResult: true
              };

              io.to(lobbyCode).emit('ball_completed', ballCompletedData);
              io.to(matchedLobby.lobbyCode).emit('ball_completed', ballCompletedData);

              // Reset game phase to shuffle for the next ball
              setTimeout(() => {
                io.to(lobbyCode).emit('game_phase_updated', { phase: 'shuffle' });
                io.to(matchedLobby.lobbyCode).emit('game_phase_updated', { phase: 'shuffle' });
              }, 5000); // 5 second delay before resetting

              return;
            }
          }
        }

        // Find the winning player
        const winningPlayer = allPlayers.find(p => p.id === winningPlayerId);
        if (!winningPlayer) {
          console.error(`Winning player ${winningPlayerId} not found in player list`);
          return callback?.({ success: false, error: 'Winning player not found' });
        }

        console.log(`Hand winner: ${winningPlayer.name} (Team ${winningPlayer.team}) with ${winningCard.value} of ${winningCard.suit}. Points: ${points}`);

        // Increment the hand counter
        lobby.currentHandId = (lobby.currentHandId || 0) + 1;
        matchedLobby.currentHandId = lobby.currentHandId;

        // Store the lead suit for this hand
        const leadSuit = lobby.currentHandCards[0].suit;

        // Track hands won by each team
        if (!lobby.hands) {
          lobby.hands = [];

          // Only initialize matchedLobby if it's different from the main lobby
          if (matchedLobby !== lobby && !matchedLobby.hands) {
            matchedLobby.hands = [];
          }
        }

        // Create a hand object to track
        const handObject = {
          id: lobby.currentHandId,
          cards: lobby.currentHandCards,
          leadSuit: leadSuit,
          winner: winningPlayer,
          points: points,
          winReason: winReason,
          winningTeam: winningPlayer.team,
          // Add plays property to track who played each card
          plays: lobby.currentHandCards.map(card => ({
            playerId: card.playedBy,
            card: {
              suit: card.suit,
              value: card.value
            },
            // Include the player's hand before playing if available
            handBeforePlay: card.handBeforePlay || []
          }))
        };

        // Check if this hand ID already exists to avoid duplicates
        const existingHandIndex = lobby.hands.findIndex(h => h.id === lobby.currentHandId);
        if (existingHandIndex !== -1) {
          console.log(`Hand ID ${lobby.currentHandId} already exists, updating instead of adding`);

          // Update the existing hand
          lobby.hands[existingHandIndex] = handObject;

          // Only update matchedLobby if it's different from the main lobby
          if (matchedLobby !== lobby) {
            const matchedExistingHandIndex = matchedLobby.hands.findIndex(h => h.id === lobby.currentHandId);
            if (matchedExistingHandIndex !== -1) {
              matchedLobby.hands[matchedExistingHandIndex] = handObject;
            }
          }
        } else {
          // Add the hand to the hands array
          lobby.hands.push(handObject);

          // Only add to matchedLobby if it's different from the main lobby
          if (matchedLobby !== lobby) {
            matchedLobby.hands.push(handObject);
          }
        }

        // Log the hands for debugging
        console.log(`Hands after adding/updating hand ${lobby.currentHandId}:`,
          lobby.hands.map(h => `ID: ${h.id}, Winner: ${h.winner.name}, Team: ${h.winningTeam}, Points: ${h.points}`));

        // Count hands won by each team (using unique hand IDs to avoid duplicates)
        const team1HandIds = new Set();
        const team2HandIds = new Set();

        lobby.hands.forEach(hand => {
          if (hand.winningTeam === 1) {
            team1HandIds.add(hand.id);
          } else if (hand.winningTeam === 2) {
            team2HandIds.add(hand.id);
          }
        });

        const team1HandsWon = team1HandIds.size;
        const team2HandsWon = team2HandIds.size;

        console.log(`Team 1 has won ${team1HandsWon} hands, Team 2 has won ${team2HandsWon} hands`);

        // Check if the winning team has won a hand
        // This is important for Jordhi validation
        if (team1HandsWon > 0 || team2HandsWon > 0) {
          const handCount = winningPlayer.team === 1 ? team1HandsWon : team2HandsWon;
          const suffix = handCount === 1 ? 'st' : handCount === 2 ? 'nd' : handCount === 3 ? 'rd' : 'th';
          console.log(`Team ${winningPlayer.team} has won their ${handCount}${suffix} hand. Jordhi can be called now.`);
        }

        // Send hand_completed event to all players
        const handCompletedData = {
          handId: lobby.currentHandId,
          cards: lobby.currentHandCards,
          winner: winningPlayer,
          nextTurn: winningPlayerId,
          points: points,
          leadSuit: leadSuit,
          winReason: winReason,
          winningTeam: winningPlayer.team,
          team1HandsWon: team1HandsWon,
          team2HandsWon: team2HandsWon
        };

        // Wait 2 seconds before clearing the played cards and setting the next turn
        setTimeout(() => {
          // Clear the played cards for the next hand
          lobby.currentHandCards = [];

          // Only clear matchedLobby if it's different from the main lobby
          if (matchedLobby !== lobby) {
            matchedLobby.currentHandCards = [];
          }

          // Send hand_completed event to both lobbies
          io.to(lobbyCode).emit('hand_completed', handCompletedData);
          io.to(matchedLobby.lobbyCode).emit('hand_completed', handCompletedData);

          // Initialize or update team points for this ball
          if (!lobby.ballPoints) {
            lobby.ballPoints = { team1: 0, team2: 0 };
            matchedLobby.ballPoints = { team1: 0, team2: 0 };
          }

          // Add points to the winning team
          const winningTeam = winningPlayer.team;

          // Log the current points before adding
          console.log(`Before adding points: Team ${winningTeam} has ${lobby.ballPoints[`team${winningTeam}`]} points`);

          // Add points to the winning team
          lobby.ballPoints[`team${winningTeam}`] += points;
          matchedLobby.ballPoints[`team${winningTeam}`] += points;

          // Verify the points were added correctly
          console.log(`After adding ${points} points: Team ${winningTeam} now has ${lobby.ballPoints[`team${winningTeam}`]} points`);

          // Calculate the expected total by summing points from all hands won by this team
          const handsWonByTeam = lobby.hands.filter(h => h.winningTeam === winningTeam);
          const uniqueHandIds = new Set();
          const uniqueHandsWonByTeam = handsWonByTeam.filter(h => {
            if (uniqueHandIds.has(h.id)) {
              return false;
            }
            uniqueHandIds.add(h.id);
            return true;
          });

          const expectedTotal = uniqueHandsWonByTeam.reduce((sum, h) => sum + (h.points || 0), 0);
          console.log(`Expected total for Team ${winningTeam} based on hands: ${expectedTotal}`);

          // If there's a mismatch, correct it
          if (expectedTotal !== lobby.ballPoints[`team${winningTeam}`]) {
            console.error(`Points mismatch for Team ${winningTeam}! Expected ${expectedTotal} but got ${lobby.ballPoints[`team${winningTeam}`]}`);
            console.log(`Correcting points for Team ${winningTeam} to ${expectedTotal}`);

            // Correct the points
            lobby.ballPoints[`team${winningTeam}`] = expectedTotal;
            matchedLobby.ballPoints[`team${winningTeam}`] = expectedTotal;
          }

          console.log(`Team ${winningTeam} now has ${lobby.ballPoints[`team${winningTeam}`]} points in this ball`);

          // Check if there was a Double call for this hand
          let doubleProcessed = false;
          if (lobby.doubleCallerId) {
            console.log(`Double was called by player ${lobby.doubleCallerId}. Processing Double outcome...`);

            // doubleHandler is already imported at the top

            // Process the Double outcome
            const doubleResult = doubleHandler.processDoubleOutcome(lobby, matchedLobby, lobby.doubleCallerId, winningPlayerId);

            if (doubleResult.success) {
              console.log('Double outcome processed successfully:', doubleResult);
              doubleProcessed = true;

              // Store the doubleResult in the lobby object for later use
              lobby.lastDoubleResult = doubleResult;
              matchedLobby.lastDoubleResult = doubleResult;

              // Emit double_outcome event to both lobbies
              io.to(lobbyCode).emit('double_outcome', doubleResult);
              io.to(matchedLobby.lobbyCode).emit('double_outcome', doubleResult);

              // Clear the Double caller ID
              lobby.doubleCallerId = null;
              matchedLobby.doubleCallerId = null;
            } else {
              console.error('Failed to process Double outcome:', doubleResult.error);
            }
          }

          // Check if this was the last hand of the ball (6 hands per ball)
          if (lobby.currentHandId >= 6) {
            console.log('Ball completed, calculating winner and next dealer...');

            // Set flag to indicate ball completion is in progress
            lobby.ballCompleted = true;
            matchedLobby.ballCompleted = true;

            // Check if there's an active Khanak call
            if (lobby.currentKhanakCallerId) {
              console.log(`Khanak was called by player ${lobby.currentKhanakCallerId}. Processing Khanak outcome...`);

              // khanakHandler is already imported at the top

              // Find the Khanak caller player
              const khanakCallerPlayer = allPlayers.find(p => p.id === lobby.currentKhanakCallerId);
              if (!khanakCallerPlayer) {
                console.error(`Khanak caller player ${lobby.currentKhanakCallerId} not found in players list`);
                // Continue with normal ball completion
              } else {
                // Process the Khanak result
                const khanakResult = khanakHandler.processKhanakResult(lobby, matchedLobby, winningPlayer);

                if (khanakResult) {
                  console.log('Khanak outcome processed:', khanakResult);

                  // Store the khanakResult in the lobby object for later use
                  lobby.lastKhanakResult = khanakResult;
                  matchedLobby.lastKhanakResult = khanakResult;

                  // Update the ball scores
                  if (!lobby.ballScores) {
                    lobby.ballScores = { team1: 0, team2: 0 };
                    matchedLobby.ballScores = { team1: 0, team2: 0 };
                  }

                  // Award balls to the winning team
                  lobby.ballScores[`team${khanakResult.winningTeam}`] += khanakResult.ballsAwarded;
                  matchedLobby.ballScores[`team${khanakResult.winningTeam}`] += khanakResult.ballsAwarded;

                  // Emit khanak_outcome event to both lobbies
                  io.to(lobbyCode).emit('khanak_outcome', khanakResult);
                  io.to(matchedLobby.lobbyCode).emit('khanak_outcome', khanakResult);

                  // Determine the next dealer based on ball scores
                  console.log(`Determining next dealer after Khanak outcome with ball scores: Team 1: ${lobby.ballScores.team1}, Team 2: ${lobby.ballScores.team2}`);
                  const nextDealerId = ballUtils.determineNextDealer(lobby, allPlayers);

                  // Update dealer
                  lobby.dealerId = nextDealerId;
                  matchedLobby.dealerId = nextDealerId;

                  // Reset for next ball
                  resetUtils.resetBallState(lobby, matchedLobby);

                  // Reset hand counter and points
                  lobby.currentHandId = 0;
                  matchedLobby.currentHandId = 0;
                  lobby.ballPoints = { team1: 0, team2: 0 };
                  matchedLobby.ballPoints = { team1: 0, team2: 0 };

                  // Get the current ball ID
                  const ballId = (lobby.currentBallId || 0) + 1;
                  lobby.currentBallId = ballId;
                  matchedLobby.currentBallId = ballId;

                  // Send ball_completed event to both lobbies
                  const ballCompletedData = {
                    ballId,
                    winner: khanakResult.winningTeam,
                    points: {
                      team1: lobby.ballPoints.team1,
                      team2: lobby.ballPoints.team2
                    },
                    nextDealer: nextDealerId,
                    ballScores: lobby.ballScores,
                    khanakProcessed: true,
                    khanakResult: {
                      success: khanakResult.success,
                      reason: khanakResult.reason,
                      ballsAwarded: khanakResult.ballsAwarded,
                      winningTeam: khanakResult.winningTeam,
                      threshold: khanakResult.khanakCall?.threshold,
                      opposingTeamPoints: khanakResult.khanakCall?.opposingTeamPoints,
                      teamJordhiPoints: khanakResult.khanakCall?.teamJordhiPoints,
                      opposingTeamJordhiPoints: khanakResult.khanakCall?.opposingTeamJordhiPoints,
                      opposingTeamHands: khanakResult.khanakCall?.opposingTeamHands
                    }
                  };

                  io.to(lobbyCode).emit('ball_completed', ballCompletedData);
                  io.to(matchedLobby.lobbyCode).emit('ball_completed', ballCompletedData);

                  // Add ball to game history
                  gameEndUtils.addBallToHistory(lobby, ballCompletedData);
                  if (matchedLobby !== lobby) {
                    gameEndUtils.addBallToHistory(matchedLobby, ballCompletedData);
                  }

                  // Mark winning Khanak if applicable
                  if (khanakResult.success) {
                    gameEndUtils.markWinningKhanak(lobby, khanakResult);
                    if (matchedLobby !== lobby) {
                      gameEndUtils.markWinningKhanak(matchedLobby, khanakResult);
                    }
                  }

                  // Check if game should end
                  const gameEndCheck = gameEndUtils.checkGameEnd(lobby);
                  if (gameEndCheck.gameEnded) {
                    console.log(`Game ended! Team ${gameEndCheck.winner} wins!`);

                    const gameHistory = gameEndUtils.getGameHistory(lobby);
                    const gameEndData = {
                      ...gameEndCheck,
                      gameHistory
                    };

                    // Emit game_ended event to both lobbies
                    io.to(lobbyCode).emit('game_ended', gameEndData);
                    io.to(matchedLobby.lobbyCode).emit('game_ended', gameEndData);

                    return; // Don't continue to next ball
                  }

                  console.log(`Ball ${ballId} completed after Khanak. Winner: Team ${khanakResult.winningTeam}. Next dealer: ${nextDealerId}`);

                  // Reset game phase to shuffle for the next ball
                  // Use a longer delay (15 seconds) to ensure the BallResultsDisplay has time to show
                  setTimeout(() => {
                    console.log(`Updating game phase to shuffle after Khanak outcome for ball ${ballId}`);
                    io.to(lobbyCode).emit('game_phase_updated', { phase: 'shuffle' });
                    io.to(matchedLobby.lobbyCode).emit('game_phase_updated', { phase: 'shuffle' });
                  }, 15000); // 15 second delay before updating phase

                  // Clear the Khanak caller ID
                  lobby.currentKhanakCallerId = null;
                  matchedLobby.currentKhanakCallerId = null;
                  lobby.currentKhanakCallerTeam = null;
                  matchedLobby.currentKhanakCallerTeam = null;
                  lobby.currentKhanakCall = null;
                  matchedLobby.currentKhanakCall = null;

                  return;
                }
              }
            }

            // If Double was processed successfully, we need to skip the normal ball completion logic
            // and just handle the dealer rotation and reset
            if (doubleProcessed) {
              console.log('Double was processed successfully, skipping normal ball completion logic');

              // Log the current dealer and their team before determining the next dealer
              const currentDealer = allPlayers.find(p => p.id === lobby.dealerId);
              if (currentDealer) {
                console.log(`Current dealer before Double outcome: ${currentDealer.name} (${currentDealer.id}) - Team ${currentDealer.team}`);
              }

              // Log the ball scores that will be used to determine the next dealer
              console.log(`Ball scores for dealer determination: Team 1: ${lobby.ballScores.team1}, Team 2: ${lobby.ballScores.team2}`);

              // The dealer's team will be determined in ballUtils.determineNextDealer

              // Determine the next dealer based on ball scores
              console.log(`Determining next dealer after Double outcome with ball scores: Team 1: ${lobby.ballScores.team1}, Team 2: ${lobby.ballScores.team2}`);
              const nextDealerId = ballUtils.determineNextDealer(lobby, allPlayers);

              // Find the next dealer player for logging
              const nextDealer = allPlayers.find(p => p.id === nextDealerId);
              if (nextDealer) {
                console.log(`Next dealer after Double outcome: ${nextDealer.name} (${nextDealer.id}) - Team ${nextDealer.team} at position ${nextDealer.position}`);
              }

              // Update dealer
              lobby.dealerId = nextDealerId;
              matchedLobby.dealerId = nextDealerId;

              // Reset for next ball
              resetUtils.resetBallState(lobby, matchedLobby);

              // Reset hand counter and points
              lobby.currentHandId = 0;
              matchedLobby.currentHandId = 0;
              lobby.ballPoints = { team1: 0, team2: 0 };
              matchedLobby.ballPoints = { team1: 0, team2: 0 };

              // Get the current ball ID
              const ballId = (lobby.currentBallId || 0) + 1;
              lobby.currentBallId = ballId;
              matchedLobby.currentBallId = ballId;

              // Send ball_completed event to both lobbies
              const ballCompletedData = {
                ballId,
                winner: lobby.lastDoubleResult.winningTeam,
                points: {
                  team1: lobby.ballPoints.team1,
                  team2: lobby.ballPoints.team2
                },
                nextDealer: nextDealerId,
                ballScores: lobby.ballScores,
                doubleProcessed: true,
                ballsAwarded: lobby.lastDoubleResult.ballsAwarded
              };

              io.to(lobbyCode).emit('ball_completed', ballCompletedData);
              io.to(matchedLobby.lobbyCode).emit('ball_completed', ballCompletedData);

              // Add ball to game history
              gameEndUtils.addBallToHistory(lobby, ballCompletedData);
              if (matchedLobby !== lobby) {
                gameEndUtils.addBallToHistory(matchedLobby, ballCompletedData);
              }

              // Check if game should end
              const gameEndCheck = gameEndUtils.checkGameEnd(lobby);
              if (gameEndCheck.gameEnded) {
                console.log(`Game ended! Team ${gameEndCheck.winner} wins!`);

                const gameHistory = gameEndUtils.getGameHistory(lobby);
                const gameEndData = {
                  ...gameEndCheck,
                  gameHistory
                };

                // Emit game_ended event to both lobbies
                io.to(lobbyCode).emit('game_ended', gameEndData);
                io.to(matchedLobby.lobbyCode).emit('game_ended', gameEndData);

                return; // Don't continue to next ball
              }

              console.log(`Ball ${ballId} completed after Double. Winner: Team ${lobby.lastDoubleResult.winningTeam}. Next dealer: ${nextDealerId}`);

              // Reset game phase to shuffle for the next ball
              // Use a longer delay (20 seconds) to ensure the BallResultsDisplay has time to show
              // and players have time to click the Continue Game button
              // This is longer than the 10 seconds for the BallResultsDisplay to ensure
              // the game phase is updated after the player clicks Continue Game
              setTimeout(() => {
                console.log(`Updating game phase to shuffle after Double outcome for ball ${ballId}`);
                io.to(lobbyCode).emit('game_phase_updated', { phase: 'shuffle' });
                io.to(matchedLobby.lobbyCode).emit('game_phase_updated', { phase: 'shuffle' });
              }, 20000); // 20 second delay before updating phase

              return;
            }

            // Check if there's a Thunee player who won all 6 hands
            if (lobby.thuneePlayer && lobby.thuneePlayer === winningPlayerId) {
              console.log(`Thunee player ${winningPlayer.name} (${winningPlayerId}) won all 6 hands!`);

              // Award 4 balls to the Thunee player's team
              const thuneePlayerTeam = winningPlayer.team;
              const ballsToAward = 4;

              // Update the ball scores
              if (!lobby.ballScores) {
                lobby.ballScores = { team1: 0, team2: 0 };
                matchedLobby.ballScores = { team1: 0, team2: 0 };
              }

              lobby.ballScores[`team${thuneePlayerTeam}`] += ballsToAward;
              matchedLobby.ballScores[`team${thuneePlayerTeam}`] += ballsToAward;

              console.log(`Thunee success! Team ${thuneePlayerTeam} awarded ${ballsToAward} balls`);

              // Prepare comprehensive Thunee success data
              const thuneeSuccessData = {
                thuneePlayerId: winningPlayerId,
                thuneePlayerName: winningPlayer.name,
                thuneePlayerTeam: thuneePlayerTeam,
                ballsAwarded: ballsToAward,
                ballScores: lobby.ballScores,
                // Add comprehensive hand history
                handsPlayed: lobby.hands || [],
                allHandsWon: true,
                totalHands: 6,
                trumpSuit: lobby.gameState?.trumpSuit,
                instantResult: false // Success is only determined after all 6 hands
              };

              // Notify all players of the Thunee success
              io.to(lobbyCode).emit('thunee_success', thuneeSuccessData);

              if (matchedLobby.lobbyCode !== lobbyCode) {
                io.to(matchedLobby.lobbyCode).emit('thunee_success', thuneeSuccessData);
              }

              // Check if game should end after Thunee success
              const gameEndUtils = require('./src/utils/server/gameEndUtils');
              const thuneeSuccessGameEndCheck = gameEndUtils.checkGameEnd(lobby);
              if (thuneeSuccessGameEndCheck.gameEnded) {
                console.log(`Game ended after Thunee success! Team ${thuneeSuccessGameEndCheck.winner} wins!`);

                const gameHistory = gameEndUtils.getGameHistory(lobby);
                const gameEndData = {
                  ...thuneeSuccessGameEndCheck,
                  gameHistory
                };

                // Emit game_ended event to both lobbies
                io.to(lobbyCode).emit('game_ended', gameEndData);
                io.to(matchedLobby.lobbyCode).emit('game_ended', gameEndData);

                return; // Don't continue with normal ball processing
              }

              // Clear the Thunee player since the Thunee attempt succeeded
              lobby.thuneePlayer = null;
              matchedLobby.thuneePlayer = null;

              // Determine the next dealer based on ball scores
              console.log(`Determining next dealer after Thunee success with ball scores: Team 1: ${lobby.ballScores.team1}, Team 2: ${lobby.ballScores.team2}`);
              const nextDealerId = ballUtils.determineNextDealer(lobby, allPlayers);

              // Update dealer
              lobby.dealerId = nextDealerId;
              matchedLobby.dealerId = nextDealerId;

              // Reset for next ball
              resetUtils.resetBallState(lobby, matchedLobby);

              // Reset hand counter and points
              lobby.currentHandId = 0;
              matchedLobby.currentHandId = 0;
              lobby.ballPoints = { team1: 0, team2: 0 };
              matchedLobby.ballPoints = { team1: 0, team2: 0 };

              // Get the current ball ID
              const ballId = (lobby.currentBallId || 0) + 1;
              lobby.currentBallId = ballId;
              matchedLobby.currentBallId = ballId;

              // Send ball_completed event to both lobbies
              const ballCompletedData = {
                ballId,
                winner: thuneePlayerTeam,
                points: {
                  team1: lobby.ballPoints.team1,
                  team2: lobby.ballPoints.team2
                },
                nextDealer: nextDealerId,
                ballScores: lobby.ballScores,
                thuneeSuccess: true,
                ballsAwarded: ballsToAward
              };

              io.to(lobbyCode).emit('ball_completed', ballCompletedData);
              io.to(matchedLobby.lobbyCode).emit('ball_completed', ballCompletedData);

              // Add ball to game history
              gameEndUtils.addBallToHistory(lobby, ballCompletedData);
              if (matchedLobby !== lobby) {
                gameEndUtils.addBallToHistory(matchedLobby, ballCompletedData);
              }

              // Check if game should end
              const gameEndCheck = gameEndUtils.checkGameEnd(lobby);
              if (gameEndCheck.gameEnded) {
                console.log(`Game ended! Team ${gameEndCheck.winner} wins!`);

                const gameHistory = gameEndUtils.getGameHistory(lobby);
                const gameEndData = {
                  ...gameEndCheck,
                  gameHistory
                };

                // Emit game_ended event to both lobbies
                io.to(lobbyCode).emit('game_ended', gameEndData);
                io.to(matchedLobby.lobbyCode).emit('game_ended', gameEndData);

                return; // Don't continue to next ball
              }

              // Reset game phase to shuffle for the next ball
              setTimeout(() => {
                io.to(lobbyCode).emit('game_phase_updated', { phase: 'shuffle' });
                io.to(matchedLobby.lobbyCode).emit('game_phase_updated', { phase: 'shuffle' });
              }, 5000); // 5 second delay before resetting

              return;
            }

            // Normal ball completion (not a Thunee success)
            // Calculate the ball winner with Jordhi rules and final hand evaluation

            // Recalculate the points from the hands to ensure accuracy
            const team1Hands = lobby.hands.filter(h => h.winningTeam === 1);
            const team2Hands = lobby.hands.filter(h => h.winningTeam === 2);

            // Create sets to track unique hand IDs
            const team1HandIds = new Set();
            const team2HandIds = new Set();

            // Filter out duplicate hands
            const uniqueTeam1Hands = team1Hands.filter(h => {
              if (team1HandIds.has(h.id)) {
                return false;
              }
              team1HandIds.add(h.id);
              return true;
            });

            const uniqueTeam2Hands = team2Hands.filter(h => {
              if (team2HandIds.has(h.id)) {
                return false;
              }
              team2HandIds.add(h.id);
              return true;
            });

            // Calculate the points
            const calculatedTeam1Points = uniqueTeam1Hands.reduce((sum, h) => sum + (h.points || 0), 0);
            const calculatedTeam2Points = uniqueTeam2Hands.reduce((sum, h) => sum + (h.points || 0), 0);

            // Log the calculated points vs. the stored points
            console.log(`Calculated points - Team 1: ${calculatedTeam1Points}, Team 2: ${calculatedTeam2Points}`);
            console.log(`Stored points - Team 1: ${lobby.ballPoints.team1}, Team 2: ${lobby.ballPoints.team2}`);

            // Use the calculated points to ensure accuracy
            const team1Points = calculatedTeam1Points;
            const team2Points = calculatedTeam2Points;

            // Update the stored points to match the calculated points
            lobby.ballPoints.team1 = team1Points;
            lobby.ballPoints.team2 = team2Points;
            matchedLobby.ballPoints.team1 = team1Points;
            matchedLobby.ballPoints.team2 = team2Points;

            // Get the last hand winner's team for the final hand adjustment
            const lastHandWinningTeam = winningPlayer.team;

            // Call the updated calculateBallWinner function with the last hand winner's team
            const ballResult = ballUtils.calculateBallWinner(lobby, team1Points, team2Points, lastHandWinningTeam);

            // Initialize ball scores if not already done
            if (!lobby.ballScores) {
              lobby.ballScores = { team1: 0, team2: 0 };
              matchedLobby.ballScores = { team1: 0, team2: 0 };
            }

            // Update ball scores
            lobby.ballScores = ballResult.ballScores;
            matchedLobby.ballScores = ballResult.ballScores;

            // Determine the next dealer based on ball scores
            console.log(`Determining next dealer after normal ball completion with ball scores: Team 1: ${lobby.ballScores.team1}, Team 2: ${lobby.ballScores.team2}`);
            const nextDealerId = ballUtils.determineNextDealer(lobby, allPlayers);

            // Update dealer
            lobby.dealerId = nextDealerId;
            matchedLobby.dealerId = nextDealerId;

            // Reset for next ball
            resetUtils.resetBallState(lobby, matchedLobby);

            // Reset hand counter and points
            lobby.currentHandId = 0;
            matchedLobby.currentHandId = 0;
            lobby.ballPoints = { team1: 0, team2: 0 };
            matchedLobby.ballPoints = { team1: 0, team2: 0 };

            // Get the current ball ID
            const ballId = (lobby.currentBallId || 0) + 1;
            lobby.currentBallId = ballId;
            matchedLobby.currentBallId = ballId;

            // Send ball_completed event to both lobbies with additional data for Jordhi rules
            const ballCompletedData = {
              ballId,
              winner: ballResult.winner,
              points: {
                team1: team1Points,
                team2: team2Points
              },
              nextDealer: nextDealerId,
              ballScores: ballResult.ballScores,
              // Additional data for Jordhi rules and final hand evaluation
              targetScore: ballResult.targetScore,
              initialTargetScore: ballResult.initialTargetScore,
              jordhiAdjustments: ballResult.jordhiAdjustments,
              finalHandAdjustment: ballResult.finalHandAdjustment,
              nonTrumpingTeamPoints: ballResult.nonTrumpingTeamPoints,
              trumpingTeam: ballResult.trumpingTeam,
              handsWonByNonTrumpingTeam: ballResult.handsWonByNonTrumpingTeam,
              ballsAwarded: ballResult.ballsAwarded || 1, // Use the calculated balls awarded or default to 1
              isCallAndLost: ballResult.isCallAndLost || false // Include whether this was a "Call and lost" scenario
            };

            io.to(lobbyCode).emit('ball_completed', ballCompletedData);
            io.to(matchedLobby.lobbyCode).emit('ball_completed', ballCompletedData);

            // Add ball to game history
            gameEndUtils.addBallToHistory(lobby, ballCompletedData);
            if (matchedLobby !== lobby) {
              gameEndUtils.addBallToHistory(matchedLobby, ballCompletedData);
            }

            // Check if game should end
            const gameEndCheck = gameEndUtils.checkGameEnd(lobby);
            if (gameEndCheck.gameEnded) {
              console.log(`Game ended! Team ${gameEndCheck.winner} wins!`);

              const gameHistory = gameEndUtils.getGameHistory(lobby);
              const gameEndData = {
                ...gameEndCheck,
                gameHistory
              };

              // Emit game_ended event to both lobbies
              io.to(lobbyCode).emit('game_ended', gameEndData);
              io.to(matchedLobby.lobbyCode).emit('game_ended', gameEndData);

              return; // Don't continue to next ball
            }

            console.log(`Ball ${ballId} completed. Winner: Team ${ballResult.winner}. Next dealer: ${nextDealerId}`);

            // After a delay, update the dealer and trump selector
            setTimeout(() => {
              // Find the next dealer player
              const nextDealer = allPlayers.find(p => p.id === nextDealerId);
              if (!nextDealer) {
                console.error(`Next dealer ${nextDealerId} not found in players list`);
                return;
              }

              // Determine the trump selector (player to the right of dealer)
              const dealerIndex = allPlayers.findIndex(p => p.id === nextDealerId);
              const trumpSelectorIndex = (dealerIndex + 3) % allPlayers.length;
              const trumpSelector = allPlayers[trumpSelectorIndex];

              if (!trumpSelector) {
                console.error(`Trump selector not found at index ${trumpSelectorIndex}`);
                return;
              }

              // Update dealer and trump selector
              lobby.trumpSelectorId = trumpSelector.id;
              matchedLobby.trumpSelectorId = trumpSelector.id;

              // Send dealer_updated event to both lobbies
              io.to(lobbyCode).emit('dealer_updated', {
                dealerId: nextDealerId,
                trumpSelectorId: trumpSelector.id
              });
              io.to(matchedLobby.lobbyCode).emit('dealer_updated', {
                dealerId: nextDealerId,
                trumpSelectorId: trumpSelector.id
              });

              console.log(`Dealer updated to ${nextDealer.name} (${nextDealerId}), Trump selector: ${trumpSelector.name} (${trumpSelector.id})`);

              // Reset game phase to shuffle for the next ball
              // This ensures we follow the same flow as when the game starts:
              // 1. Dealer shuffles
              // 2. Opponent gets to cut
              // 3. Dealer deals 4 cards
              // 4. Bidding phase for trump
              // 5. Dealer deals the last 2 cards
              // 6. Person to the right of the trumper plays first
              io.to(lobbyCode).emit('game_phase_updated', { phase: 'shuffle' });
              io.to(matchedLobby.lobbyCode).emit('game_phase_updated', { phase: 'shuffle' });
            }, 3000); // 3 second delay before updating dealer
          } else {
            // Not the last hand, continue with the next hand
            // Set the winner as the next player
            console.log(`Setting next player turn to ${winningPlayerId} (winner of the hand)`);

            // Stop any existing timers before setting the new turn
            const turnUtils = require('./src/utils/server/turnUtils');
            turnUtils.stopTurnTimer(lobby);
            turnUtils.stopTurnTimer(matchedLobby);

            // Use the turnUtils to set the next player's turn with a timer
            turnUtils.setPlayerTurn(io, lobby, matchedLobby, winningPlayerId, false);
          }
        }, 2000); // 2 second delay to allow players to see the played cards
      } else {
        // Not all players have played a card yet, determine the next player
        // Get the current player
        const currentPlayer = allPlayers.find(p => p.id === socket.id);
        if (!currentPlayer || !currentPlayer.position) {
          console.error(`Current player ${socket.id} not found or has no position assigned`);
          return callback?.({ success: false, error: 'Current player position not found' });
        }

        // Log all players and their positions for debugging
        console.log("All players in order for turn determination:");
        allPlayers.forEach((p) => {
          console.log(`Player: ${p.name} (${p.id}) - Team ${p.team} - Position ${p.position}`);
        });

        // Get the unique player IDs who have already played cards
        const playedPlayerIds = lobby.currentHandCards.map(card => card.playedBy);
        console.log(`Players who have already played: ${playedPlayerIds.join(', ')}`);

        // Use the playerPositionUtils to get the player to the right (counter-clockwise)
        const nextPosition = playerPositionUtils.getPositionToRight(currentPlayer.position);
        console.log(`Current player position: ${currentPlayer.position}, Next position: ${nextPosition}`);

        // Find the player with the next position
        const nextPlayer = allPlayers.find(p => p.position === nextPosition);
        if (!nextPlayer) {
          console.error(`No player found at position ${nextPosition}`);

          // Try to find any player who hasn't played yet
          const unplayedPlayers = allPlayers.filter(p => !playedPlayerIds.includes(p.id));

          if (unplayedPlayers.length > 0) {
            const fallbackNextPlayer = unplayedPlayers[0];
            console.log(`Falling back to next unplayed player: ${fallbackNextPlayer.name} (${fallbackNextPlayer.id}) at position ${fallbackNextPlayer.position}`);

            // Use the turnUtils to set the next player's turn with a timer
            turnUtils.setPlayerTurn(io, lobby, matchedLobby, fallbackNextPlayer.id, false);

            return callback?.({ success: true });
          }

          return callback?.({ success: false, error: 'Next player not found' });
        }

        // Check if the next player has already played a card
        if (playedPlayerIds.includes(nextPlayer.id)) {
          console.log(`Next player ${nextPlayer.name} has already played a card. Looking for another player.`);

          // Find any player who hasn't played yet
          const unplayedPlayers = allPlayers.filter(p => !playedPlayerIds.includes(p.id));

          if (unplayedPlayers.length > 0) {
            const fallbackNextPlayer = unplayedPlayers[0];
            console.log(`Falling back to next unplayed player: ${fallbackNextPlayer.name} (${fallbackNextPlayer.id}) at position ${fallbackNextPlayer.position}`);

            // Use the turnUtils to set the next player's turn with a timer
            turnUtils.setPlayerTurn(io, lobby, matchedLobby, fallbackNextPlayer.id, false);

            return callback?.({ success: true });
          }

          console.error('No unplayed players found, but not all players have played yet. This should not happen.');
          return callback?.({ success: false, error: 'No unplayed players found' });
        }

        // In Thunee, play moves counter-clockwise
        console.log(`Player ${socket.id} played a card. Next player: ${nextPlayer.id} (${nextPlayer.name}) at position ${nextPosition}`);

        // Use the turnUtils to set the next player's turn with a timer
        turnUtils.setPlayerTurn(io, lobby, matchedLobby, nextPlayer.id, false);
      }

      callback?.({ success: true });
    } catch (error) {
      console.error('Error handling play card:', error);
      callback?.({ success: false, error: error.message });
    }
  });

  // Handle request for missing cards
  socket.on('request_missing_cards', (_, callback) => {
    try {
      console.log('request_missing_cards event received from client:', socket.id);

      // Get lobby code
      const lobbyCode = socketToLobby.get(socket.id);
      if (!lobbyCode || !lobbies.has(lobbyCode)) {
        console.error('Lobby not found for socket ID:', socket.id);
        return callback?.({ success: false, error: 'Lobby not found' });
      }

      const lobby = lobbies.get(lobbyCode);
      const matchedLobby = lobbies.get(lobby.matchedLobby);

      if (!matchedLobby) {
        console.error(`Matched lobby ${lobby.matchedLobby} not found`);
        return callback?.({ success: false, error: 'Matched lobby not found' });
      }

      console.log(`Player ${socket.id} is requesting missing cards`);

      // First check if we have stored player cards
      if (lobby.playerCards && lobby.playerCards[socket.id]) {
        const playerCards = lobby.playerCards[socket.id];
        console.log(`Player ${socket.id} has ${playerCards.length} cards stored in lobby`);

        // If player already has 6 cards, just send them again
        if (playerCards.length === 6) {
          console.log(`Player ${socket.id} already has 6 cards, resending them`);
          socket.emit('receive_cards', { cards: playerCards });
          return callback?.({ success: true });
        }
      }

      // If we get here, either the player doesn't have cards stored or doesn't have all 6
      // Use our finalCardDealer to ensure all players have their cards
      console.log('Using finalCardDealer to ensure all players have their cards');
      finalCardDealer.dealFinalCards(io, lobby, matchedLobby);

      // After a short delay, check if the player now has cards
      setTimeout(() => {
        if (lobby.playerCards && lobby.playerCards[socket.id]) {
          const playerCards = lobby.playerCards[socket.id];
          console.log(`After finalCardDealer, player ${socket.id} has ${playerCards.length} cards`);

          // Send the cards to the player again
          socket.emit('receive_cards', { cards: playerCards });
        } else {
          console.error(`Still no cards found for player ${socket.id} after finalCardDealer`);
        }
      }, 1000);

      callback?.({ success: true });
    } catch (error) {
      console.error('Error in request_missing_cards:', error);
      callback?.({ success: false, error: error.message });
    }
  });

  // Handle set dealer event
  socket.on('set_dealer', (data, callback) => {
    handleSetDealer(io, socket, data, callback, lobbies, socketToLobby, socketToGameLobby);
  });

  // Handle hold game event
  socket.on('hold_game', (data, callback) => {
    try {
      console.log('hold_game event received from client:', socket.id, 'data:', data);

      // Get lobby code
      const lobbyCode = socketToLobby.get(socket.id);
      if (!lobbyCode || !lobbies.has(lobbyCode)) {
        console.error('Lobby not found for socket ID:', socket.id);
        return callback?.({ success: false, error: 'Lobby not found' });
      }

      const lobby = lobbies.get(lobbyCode);

      // Check if this is a single lobby with all players (invite code case)
      const team1Count = lobby.teams[1]?.length || 0;
      const team2Count = lobby.teams[2]?.length || 0;
      const totalPlayers = team1Count + team2Count;
      const isSingleLobby = totalPlayers === 4;

      console.log(`Single lobby check: team1=${team1Count}, team2=${team2Count}, total=${totalPlayers}, isSingle=${isSingleLobby}`);

      // If this is a single lobby, use the same lobby as the matched lobby
      const matchedLobby = isSingleLobby ? lobby : lobbies.get(lobby.matchedLobby);

      if (!matchedLobby) {
        console.error(`Matched lobby ${lobby.matchedLobby} not found, using current lobby as fallback`);
        // Use the current lobby as a fallback
      }

      // Find the player who initiated the hold
      const player = lobby.players.find(p => p.id === socket.id);
      if (!player) {
        console.error(`Player ${socket.id} not found in lobby ${lobbyCode}`);
        return callback?.({ success: false, error: 'Player not found' });
      }

      // Get the player role and duration from the data
      const playerRole = data.playerRole || "remaining-player";
      const duration = data.duration || (playerRole === "trumper" ? 5 :
                                        playerRole === "first-remaining" ? 3 : 2);

      console.log(`Player ${player.name} (${player.id}) initiated a game hold as ${playerRole} for ${duration} seconds`);

      // Notify all players in both lobbies that the game is being held
      io.to(lobbyCode).emit('game_held', {
        playerId: socket.id,
        playerName: player.name,
        duration: duration,
        playerRole: playerRole
      });

      // If matchedLobby exists and is different from the current lobby, notify it too
      if (matchedLobby && matchedLobby.lobbyCode !== lobbyCode) {
        io.to(matchedLobby.lobbyCode).emit('game_held', {
          playerId: socket.id,
          playerName: player.name,
          duration: duration,
          playerRole: playerRole
        });
      }

      // Send a specific thunee opportunity event to the player who initiated the hold
      io.to(socket.id).emit('thunee_opportunity', {
        playerId: socket.id,
        playerName: player.name,
        stage: playerRole,
        duration: duration
      });

      // After the specified duration, notify all players that the hold is complete
      setTimeout(() => {
        console.log(`Game hold initiated by ${player.name} (${player.id}) as ${playerRole} is complete`);

        // Determine the next stage based on the current stage
        let nextStage = null;
        if (playerRole === "first-remaining") {
          nextStage = "last-remaining";
        }

        io.to(lobbyCode).emit('game_hold_complete', {
          playerId: socket.id,
          playerName: player.name,
          nextStage: nextStage
        });

        // If matchedLobby exists and is different from the current lobby, notify it too
        if (matchedLobby && matchedLobby.lobbyCode !== lobbyCode) {
          io.to(matchedLobby.lobbyCode).emit('game_hold_complete', {
            playerId: socket.id,
            playerName: player.name,
            nextStage: nextStage
          });
        }
      }, duration * 1000);

      callback?.({ success: true });
    } catch (error) {
      console.error('Error in hold_game:', error);
      callback?.({ success: false, error: error.message });
    }
  });

  // Handle call thunee event
  socket.on('call_thunee', (_, callback) => {
    try {
      console.log('call_thunee event received from client:', socket.id);

      // Get lobby code
      const lobbyCode = socketToLobby.get(socket.id);
      if (!lobbyCode || !lobbies.has(lobbyCode)) {
        console.error('Lobby not found for socket ID:', socket.id);
        return callback?.({ success: false, error: 'Lobby not found' });
      }

      const lobby = lobbies.get(lobbyCode);

      // Check if this is a single lobby with all players (invite code case)
      const team1Count = lobby.teams[1]?.length || 0;
      const team2Count = lobby.teams[2]?.length || 0;
      const totalPlayers = team1Count + team2Count;
      const isSingleLobby = totalPlayers === 4;

      console.log(`Single lobby check for call_thunee: team1=${team1Count}, team2=${team2Count}, total=${totalPlayers}, isSingle=${isSingleLobby}`);

      // If this is a single lobby, use the same lobby as the matched lobby
      const matchedLobby = isSingleLobby ? lobby : lobbies.get(lobby.matchedLobby);

      if (!matchedLobby) {
        console.error(`Matched lobby ${lobby.matchedLobby} not found, using current lobby as fallback`);
        // Continue with the current lobby as a fallback
      }

      // Find the player who called thunee
      const player = lobby.players.find(p => p.id === socket.id);
      if (!player) {
        console.error(`Player ${socket.id} not found in lobby ${lobbyCode}`);
        return callback?.({ success: false, error: 'Player not found' });
      }

      // Check if someone has already called Thunee
      if (lobby.thuneePlayer || (matchedLobby && matchedLobby.thuneePlayer)) {
        console.log(`Thunee already called by another player. Ignoring call from ${player.name}`);
        return callback?.({ success: false, error: 'Thunee already called by another player' });
      }

      console.log(`Player ${player.name} (${player.id}) called Thunee`);

      // Set this player as the Thunee player in both lobbies
      lobby.thuneePlayer = player.id;
      if (matchedLobby) {
        matchedLobby.thuneePlayer = player.id;
      }

      // Set this player to play first
      if (lobby.gameState) {
        lobby.gameState.nextPlayerId = player.id;
      } else {
        console.error('lobby.gameState is undefined');
        lobby.gameState = { nextPlayerId: player.id };
      }

      if (matchedLobby && matchedLobby.gameState) {
        matchedLobby.gameState.nextPlayerId = player.id;
      } else if (matchedLobby) {
        console.error('matchedLobby.gameState is undefined');
        matchedLobby.gameState = { nextPlayerId: player.id };
      }

      // Notify all players in both lobbies that Thunee has been called
      io.to(lobbyCode).emit('thunee_called', {
        playerId: socket.id,
        playerName: player.name,
        playerTeam: player.team,
        isFirstPlayer: true // This player will play first
      });

      if (matchedLobby && matchedLobby.lobbyCode !== lobbyCode) {
        io.to(matchedLobby.lobbyCode).emit('thunee_called', {
          playerId: socket.id,
          playerName: player.name,
          playerTeam: player.team,
          isFirstPlayer: true // This player will play first
        });
      }

      // Mark Thunee opportunities as complete since someone called Thunee
      lobby.thuneeOpportunitiesComplete = true;
      matchedLobby.thuneeOpportunitiesComplete = true;

      // Use turnUtils to set the player's turn with a timer
      // This will automatically start the timer based on the voted timeframe
      const turnUtils = require('./src/utils/server/turnUtils');
      turnUtils.setPlayerTurn(io, lobby, matchedLobby, player.id, false);

      callback?.({ success: true });
    } catch (error) {
      console.error('Error in call_thunee:', error);
      callback?.({ success: false, error: error.message });
    }
  });

  // Handle call double event
  socket.on('call_double', (_, callback) => {
    try {
      console.log('call_double event received from client:', socket.id);

      // Get lobby code
      const lobbyCode = socketToLobby.get(socket.id);
      if (!lobbyCode || !lobbies.has(lobbyCode)) {
        console.error('Lobby not found for socket ID:', socket.id);
        return callback?.({ success: false, error: 'Lobby not found' });
      }

      const lobby = lobbies.get(lobbyCode);

      // Check if this is a single lobby with all players (invite code case)
      const team1Count = lobby.teams[1]?.length || 0;
      const team2Count = lobby.teams[2]?.length || 0;
      const totalPlayers = team1Count + team2Count;
      const isSingleLobby = totalPlayers === 4;

      console.log(`Single lobby check for call_double: team1=${team1Count}, team2=${team2Count}, total=${totalPlayers}, isSingle=${isSingleLobby}`);

      // If this is a single lobby, use the same lobby as the matched lobby
      const matchedLobby = isSingleLobby ? lobby : lobbies.get(lobby.matchedLobby);

      if (!matchedLobby) {
        console.error(`Matched lobby ${lobby.matchedLobby} not found, using current lobby as fallback`);
        // Continue with the current lobby as a fallback
      }

      // Import the doubleHandler
      const doubleHandler = require('./src/handlers/doubleHandler');

      // Validate the Double call
      const validationResult = doubleHandler.validateDoubleCall(lobby, matchedLobby, socket.id);

      // Find the player who called double
      const player = lobby.players.find(p => p.id === socket.id);
      if (!player) {
        console.error(`Player ${socket.id} not found in lobby ${lobbyCode}`);
        return callback?.({ success: false, error: 'Player not found' });
      }

      console.log(`Player ${player.name} (${player.id}) called Double. Validation result:`, validationResult);

      // Store the Double caller ID in the lobby for later processing
      lobby.doubleCallerId = socket.id;
      matchedLobby.doubleCallerId = socket.id;

      // If the call is not valid, send an error notification to the player
      if (!validationResult.isValid) {
        socket.emit('double_error', {
          message: validationResult.reason
        });

        // Still notify all players that Double was called, but include validation result
        io.to(lobbyCode).emit('double_called', {
          playerId: socket.id,
          playerName: player.name,
          playerTeam: player.team,
          isValid: false,
          reason: validationResult.reason
        });

        if (matchedLobby && matchedLobby.lobbyCode !== lobbyCode) {
          io.to(matchedLobby.lobbyCode).emit('double_called', {
            playerId: socket.id,
            playerName: player.name,
            playerTeam: player.team,
            isValid: false,
            reason: validationResult.reason
          });
        }

        return callback?.({ success: false, error: validationResult.reason });
      }

      // Notify all players in both lobbies that Double has been called
      io.to(lobbyCode).emit('double_called', {
        playerId: socket.id,
        playerName: player.name,
        playerTeam: player.team,
        isValid: true
      });

      if (matchedLobby && matchedLobby.lobbyCode !== lobbyCode) {
        io.to(matchedLobby.lobbyCode).emit('double_called', {
          playerId: socket.id,
          playerName: player.name,
          playerTeam: player.team,
          isValid: true
        });
      }

      callback?.({ success: true });
    } catch (error) {
      console.error('Error in call_double:', error);
      callback?.({ success: false, error: error.message });
    }
  });

  // Handle call khanuck event
  socket.on('call_khanuck', (_, callback) => {
    try {
      console.log('call_khanuck event received from client:', socket.id);

      // Get lobby code
      const lobbyCode = socketToLobby.get(socket.id);
      if (!lobbyCode || !lobbies.has(lobbyCode)) {
        console.error('Lobby not found for socket ID:', socket.id);
        return callback?.({ success: false, error: 'Lobby not found' });
      }

      const lobby = lobbies.get(lobbyCode);

      // Check if this is a single lobby with all players (invite code case)
      const team1Count = lobby.teams[1]?.length || 0;
      const team2Count = lobby.teams[2]?.length || 0;
      const totalPlayers = team1Count + team2Count;
      const isSingleLobby = totalPlayers === 4;

      console.log(`Single lobby check for call_khanuck: team1=${team1Count}, team2=${team2Count}, total=${totalPlayers}, isSingle=${isSingleLobby}`);

      // If this is a single lobby, use the same lobby as the matched lobby
      const matchedLobby = isSingleLobby ? lobby : lobbies.get(lobby.matchedLobby);

      if (!matchedLobby) {
        console.error(`Matched lobby ${lobby.matchedLobby} not found, using current lobby as fallback`);
        // Continue with the current lobby as a fallback
      }

      // Find the player who called khanuck
      const player = lobby.players.find(p => p.id === socket.id);
      if (!player) {
        console.error(`Player ${socket.id} not found in lobby ${lobbyCode}`);
        return callback?.({ success: false, error: 'Player not found' });
      }

      console.log(`Player ${player.name} (${player.id}) called Khanuck`);

      // khanakHandler is already imported at the top

      // Process the Khanak call
      const khanakResult = khanakHandler.processKhanakCall(lobby, matchedLobby, player, io);

      // If the Khanak call is invalid (opposing team has not won any hands)
      if (!khanakResult.isValid) {
        console.log(`Invalid Khanak call: ${khanakResult.reason}`);

        // Store the Khanak call result for later use
        lobby.currentKhanakCall = {
          playerId: player.id,
          playerName: player.name,
          playerTeam: player.team,
          isValid: false,
          reason: khanakResult.reason
        };

        if (matchedLobby !== lobby) {
          matchedLobby.currentKhanakCall = lobby.currentKhanakCall;
        }

        // If the opposing team has not won any hands, they automatically lose 4 balls
        if (khanakResult.reason === 'opposing_team_no_hands') {
          console.log(`Opposing team has not won any hands. They lose 4 balls.`);

          // Update the ball scores
          if (!lobby.ballScores) {
            lobby.ballScores = { team1: 0, team2: 0 };
            matchedLobby.ballScores = { team1: 0, team2: 0 };
          }

          // Award 4 balls to the Khanak caller's team
          lobby.ballScores[`team${player.team}`] += khanakResult.ballsAwarded;
          matchedLobby.ballScores[`team${player.team}`] += khanakResult.ballsAwarded;

          // Notify all players of the invalid Khanak call
          io.to(lobbyCode).emit('khanuck_invalid', {
            playerId: player.id,
            playerName: player.name,
            playerTeam: player.team,
            reason: khanakResult.reason,
            ballsAwarded: khanakResult.ballsAwarded,
            winningTeam: khanakResult.winningTeam
          });

          if (matchedLobby && matchedLobby.lobbyCode !== lobbyCode) {
            io.to(matchedLobby.lobbyCode).emit('khanuck_invalid', {
              playerId: player.id,
              playerName: player.name,
              playerTeam: player.team,
              reason: khanakResult.reason,
              ballsAwarded: khanakResult.ballsAwarded,
              winningTeam: khanakResult.winningTeam
            });
          }

          // Reset for next ball
          resetUtils.resetBallState(lobby, matchedLobby);

          // Reset hand counter and points
          lobby.currentHandId = 0;
          matchedLobby.currentHandId = 0;
          lobby.ballPoints = { team1: 0, team2: 0 };
          matchedLobby.ballPoints = { team1: 0, team2: 0 };

          // Get the current ball ID
          const ballId = (lobby.currentBallId || 0) + 1;
          lobby.currentBallId = ballId;
          matchedLobby.currentBallId = ballId;

          // Send ball_completed event to both lobbies
          const ballCompletedData = {
            ballId,
            winner: khanakResult.winningTeam,
            points: {
              team1: 0,
              team2: 0
            },
            nextDealer: lobby.dealerId, // Keep the same dealer
            ballScores: lobby.ballScores,
            khanakInvalid: true,
            khanakReason: khanakResult.reason,
            ballsAwarded: khanakResult.ballsAwarded
          };

          io.to(lobbyCode).emit('ball_completed', ballCompletedData);
          io.to(matchedLobby.lobbyCode).emit('ball_completed', ballCompletedData);

          // Reset game phase to shuffle for the next ball
          setTimeout(() => {
            io.to(lobbyCode).emit('game_phase_updated', { phase: 'shuffle' });
            io.to(matchedLobby.lobbyCode).emit('game_phase_updated', { phase: 'shuffle' });
          }, 15000); // 15 second delay before resetting

          return callback?.({ success: true });
        }

        // If the team has not called any Jodhi, the Khanak call is invalid
        if (khanakResult.reason === 'no_jordhi_calls') {
          console.log(`Team ${player.team} has not called any Jodhi. Khanak call is invalid.`);

          // Send an error notification to the player
          socket.emit('khanuck_error', {
            message: `Your team must have called at least one Jodhi to call Khanak.`
          });

          return callback?.({ success: false, error: 'Your team must have called at least one Jodhi to call Khanak.' });
        }
      }

      // Store the current Khanak call
      lobby.currentKhanakCallerId = player.id;
      lobby.currentKhanakCallerTeam = player.team;

      if (matchedLobby !== lobby) {
        matchedLobby.currentKhanakCallerId = player.id;
        matchedLobby.currentKhanakCallerTeam = player.team;
      }

      // Notify all players in both lobbies that Khanak has been called
      io.to(lobbyCode).emit('khanuck_called', {
        playerId: socket.id,
        playerName: player.name,
        playerTeam: player.team,
        khanakThreshold: khanakResult.khanakCall?.threshold,
        teamJordhiPoints: khanakResult.khanakCall?.teamJordhiPoints,
        opposingTeamJordhiPoints: khanakResult.khanakCall?.opposingTeamJordhiPoints
      });

      if (matchedLobby && matchedLobby.lobbyCode !== lobbyCode) {
        io.to(matchedLobby.lobbyCode).emit('khanuck_called', {
          playerId: socket.id,
          playerName: player.name,
          playerTeam: player.team,
          khanakThreshold: khanakResult.khanakCall?.threshold,
          teamJordhiPoints: khanakResult.khanakCall?.teamJordhiPoints,
          opposingTeamJordhiPoints: khanakResult.khanakCall?.opposingTeamJordhiPoints
        });
      }

      callback?.({ success: true });
    } catch (error) {
      console.error('Error in call_khanuck:', error);
      callback?.({ success: false, error: error.message });
    }
  });

  // Handle pass thunee event
  socket.on('pass_thunee', (data, callback) => {
    try {
      console.log('pass_thunee event received from client:', socket.id, 'data:', data);

      // Get lobby code
      const lobbyCode = socketToLobby.get(socket.id);
      if (!lobbyCode || !lobbies.has(lobbyCode)) {
        console.error('Lobby not found for socket ID:', socket.id);
        return callback?.({ success: false, error: 'Lobby not found' });
      }

      const lobby = lobbies.get(lobbyCode);

      // Check if this is a single lobby with all players (invite code case)
      const team1Count = lobby.teams[1]?.length || 0;
      const team2Count = lobby.teams[2]?.length || 0;
      const totalPlayers = team1Count + team2Count;
      const isSingleLobby = totalPlayers === 4;

      // If this is a single lobby, use the same lobby as the matched lobby
      const matchedLobby = isSingleLobby ? lobby : lobbies.get(lobby.matchedLobby);

      if (!matchedLobby) {
        console.error(`Matched lobby ${lobby.matchedLobby} not found, using current lobby as fallback`);
        // Continue with the current lobby as a fallback
      }

      // Find the player who passed on thunee
      const player = lobby.players.find(p => p.id === socket.id);
      if (!player) {
        console.error(`Player ${socket.id} not found in lobby ${lobbyCode}`);
        return callback?.({ success: false, error: 'Player not found' });
      }

      console.log(`Player ${player.name} (${player.id}) passed on Thunee opportunity (${data.stage})`);

      // If this was the trumper, notify all players that the trumper's opportunity is complete
      if (data.stage === 'trumper') {
        console.log(`Trumper ${player.name} passed, notifying all players to show Hold Game button to non-Trumper players`);

        // Broadcast to all players in both lobbies
        io.to(lobbyCode).emit('trumper_opportunity_complete', {
          playerId: socket.id,
          playerName: player.name,
          nextStage: 'first-remaining'
        });

        if (matchedLobby && matchedLobby.lobbyCode !== lobbyCode) {
          io.to(matchedLobby.lobbyCode).emit('trumper_opportunity_complete', {
            playerId: socket.id,
            playerName: player.name,
            nextStage: 'first-remaining'
          });
        }
      }

      // If this was the last-remaining player, all Thunee opportunities are complete
      if (data.stage === 'last-remaining') {
        console.log(`Last remaining player ${player.name} passed, all Thunee opportunities are complete`);

        // Mark Thunee opportunities as complete
        lobby.thuneeOpportunitiesComplete = true;
        matchedLobby.thuneeOpportunitiesComplete = true;

        // Start the first player's turn with a timer
        if (lobby.firstPlayerId) {
          console.log(`Starting first player's turn with timer: ${lobby.firstPlayerId}`);

          // Use turnUtils to set the player's turn with a timer
          // This will automatically start the timer based on the voted timeframe
          const turnUtils = require('./src/utils/server/turnUtils');
          turnUtils.setPlayerTurn(io, lobby, matchedLobby, lobby.firstPlayerId, false);
        } else {
          console.error('No firstPlayerId found! Cannot set player turn.');
        }
      }

      callback?.({ success: true });
    } catch (error) {
      console.error('Error in pass_thunee:', error);
      callback?.({ success: false, error: error.message });
    }
  });

  // Handle trumper opportunity complete event
  socket.on('trumper_opportunity_complete', (data, callback) => {
    try {
      console.log('trumper_opportunity_complete event received from client:', socket.id, 'data:', data);

      // Get lobby code
      const lobbyCode = socketToLobby.get(socket.id);
      if (!lobbyCode || !lobbies.has(lobbyCode)) {
        console.error('Lobby not found for socket ID:', socket.id);
        return callback?.({ success: false, error: 'Lobby not found' });
      }

      const lobby = lobbies.get(lobbyCode);

      // Check if this is a single lobby with all players (invite code case)
      const team1Count = lobby.teams[1]?.length || 0;
      const team2Count = lobby.teams[2]?.length || 0;
      const totalPlayers = team1Count + team2Count;
      const isSingleLobby = totalPlayers === 4;

      // If this is a single lobby, use the same lobby as the matched lobby
      const matchedLobby = isSingleLobby ? lobby : lobbies.get(lobby.matchedLobby);

      if (!matchedLobby) {
        console.error(`Matched lobby ${lobby.matchedLobby} not found, using current lobby as fallback`);
        // Continue with the current lobby as a fallback
      }

      // Find the player who completed the trumper opportunity
      const player = lobby.players.find(p => p.id === socket.id);
      if (!player) {
        console.error(`Player ${socket.id} not found in lobby ${lobbyCode}`);
        return callback?.({ success: false, error: 'Player not found' });
      }

      console.log(`Trumper ${player.name} opportunity complete, notifying all players`);

      // Broadcast to all players in both lobbies
      io.to(lobbyCode).emit('trumper_opportunity_complete', {
        playerId: socket.id,
        playerName: player.name,
        nextStage: 'first-remaining'
      });

      if (matchedLobby && matchedLobby.lobbyCode !== lobbyCode) {
        io.to(matchedLobby.lobbyCode).emit('trumper_opportunity_complete', {
          playerId: socket.id,
          playerName: player.name,
          nextStage: 'first-remaining'
        });
      }

      // Check if all Thunee opportunities are complete
      // This is a special case where the client might send this event directly
      // after the last-remaining player has passed
      if (data.allOpportunitiesComplete === true) {
        console.log('All Thunee opportunities are complete, starting first player turn with timer');

        // Mark Thunee opportunities as complete
        lobby.thuneeOpportunitiesComplete = true;
        matchedLobby.thuneeOpportunitiesComplete = true;

        // Start the first player's turn with a timer
        if (lobby.firstPlayerId) {
          console.log(`Starting first player's turn with timer: ${lobby.firstPlayerId}`);

          // Use turnUtils to set the player's turn with a timer
          // This will automatically start the timer based on the voted timeframe
          const turnUtils = require('./src/utils/server/turnUtils');
          turnUtils.setPlayerTurn(io, lobby, matchedLobby, lobby.firstPlayerId, false);
        } else {
          console.error('No firstPlayerId found! Cannot set player turn.');
        }
      }

      callback?.({ success: true });
    } catch (error) {
      console.error('Error in trumper_opportunity_complete:', error);
      callback?.({ success: false, error: error.message });
    }
  });

  // Handle jordhi selection event
  socket.on('jordhi_selection', (data, callback) => {
    try {
      console.log('jordhi_selection event received from client:', socket.id, 'data:', data);

      // Get lobby code
      const lobbyCode = socketToLobby.get(socket.id);
      if (!lobbyCode || !lobbies.has(lobbyCode)) {
        console.error('Lobby not found for socket ID:', socket.id);
        return callback?.({ success: false, error: 'Lobby not found' });
      }

      const lobby = lobbies.get(lobbyCode);

      // Check if this is a single lobby with all players (invite code case)
      const team1Count = lobby.teams[1]?.length || 0;
      const team2Count = lobby.teams[2]?.length || 0;
      const totalPlayers = team1Count + team2Count;
      const isSingleLobby = totalPlayers === 4;

      // If this is a single lobby, use the same lobby as the matched lobby
      const matchedLobby = isSingleLobby ? lobby : lobbies.get(lobby.matchedLobby);

      if (!matchedLobby) {
        console.error(`Matched lobby ${lobby.matchedLobby} not found, using current lobby as fallback`);
        // Continue with the current lobby as a fallback
      }

      // Find the player who called jordhi
      const player = lobby.players.find(p => p.id === socket.id);
      if (!player) {
        console.error(`Player ${socket.id} not found in lobby ${lobbyCode}`);
        return callback?.({ success: false, error: 'Player not found' });
      }

      // Get the jordhi option value
      const jordhiOption = parseInt(data.jordhiOption);
      if (isNaN(jordhiOption) || ![20, 30, 40, 50].includes(jordhiOption)) {
        console.error(`Invalid jordhi option: ${data.jordhiOption}`);
        return callback?.({ success: false, error: 'Invalid jordhi option' });
      }

      // Validate if the player can call jordhi based on the game state
      // Jordhi can only be called after the team's first or third trick

      // Get the number of hands won by this player's team
      const playerTeam = player.team;

      // Make sure we're using the correct hands array
      if (!lobby.hands) {
        lobby.hands = [];
      }

      // Get unique hands won by this team to avoid counting duplicates
      const uniqueHandIds = new Set();
      const handsWonByTeam = (lobby.hands || [])
        .filter(hand => {
          if (hand.winningTeam === playerTeam) {
            // Only count each hand once by its ID
            if (!uniqueHandIds.has(hand.id)) {
              uniqueHandIds.add(hand.id);
              return true;
            }
          }
          return false;
        })
        .length;

      console.log(`Player ${player.name} (Team ${playerTeam}) is trying to call Jordhi ${jordhiOption}. Team has won ${handsWonByTeam} hands.`);

      // Check if the team has won at least one hand - this is for validation only
      // We'll still process the call even if it's invalid
      let isValidHandCount = handsWonByTeam >= 1;

      if (!isValidHandCount) {
        console.log(`Team ${playerTeam} has won ${handsWonByTeam} hands. This is an invalid Jordhi call, but will still affect target scores.`);

        // Send a notification to the player that the call is invalid but will still be processed
        socket.emit('jordhi_notification', {
          message: `Your team has not won any hands yet. This is an invalid Jordhi call, but will still affect target scores.`
        });
      }

      // Validate if the player has the correct cards for the jordhi value
      // Get the player's cards
      const playerCards = lobby.playerCards?.[socket.id] || [];

      // Get the current trump suit
      const trumpSuit = lobby.trumpSuit || null;

      // Group cards by suit
      const cardsBySuit = {};
      for (const card of playerCards) {
        if (!cardsBySuit[card.suit]) {
          cardsBySuit[card.suit] = [];
        }
        cardsBySuit[card.suit].push(card);
      }

      // Check for valid Jordhi combinations
      let isValidJordhi = false;
      let jordhiSuit = null;
      let jordhiCards = [];

      // Track which suits have already been used for valid Jordhi calls by this player
      if (!lobby.playerJordhiCalls) {
        lobby.playerJordhiCalls = {};
      }

      if (!lobby.playerJordhiCalls[socket.id]) {
        lobby.playerJordhiCalls[socket.id] = [];
      }

      // Get the player's previous valid Jordhi calls
      const playerPreviousCalls = lobby.playerJordhiCalls[socket.id];

      // Check each suit for Jordhi combinations
      for (const suit in cardsBySuit) {
        // Skip suits that have already been used for valid Jordhi calls
        if (playerPreviousCalls.some(call => call.suit === suit && call.isValid)) {
          console.log(`Skipping suit ${suit} as it has already been used for a valid Jordhi call by this player`);
          continue;
        }

        const suitCards = cardsBySuit[suit];
        const hasKing = suitCards.some(card => card.value === 'K');
        const hasQueen = suitCards.some(card => card.value === 'Q');
        const hasJack = suitCards.some(card => card.value === 'J');

        // Check if this suit has a valid Jordhi combination
        if (hasKing && hasQueen) {
          const isTrumpSuit = suit === trumpSuit;

          // Calculate the maximum possible Jordhi value based on the combination
          let maxJordhiValue = 0;
          if (isTrumpSuit && hasJack) {
            maxJordhiValue = 50;
          } else if (isTrumpSuit) {
            maxJordhiValue = 40;
          } else if (hasJack) {
            maxJordhiValue = 30;
          } else {
            maxJordhiValue = 20;
          }

          // Check if the called value is valid for this combination
          // Allow calling a lower value than the maximum possible
          // For example, if player has K+Q+J of trump (50), they can call 40
          // If player has K+Q+J of non-trump (30), they can call 20
          let isValidCall = false;

          if (maxJordhiValue === 50 && (jordhiOption === 50 || jordhiOption === 40 || jordhiOption === 20)) {
            // K+Q+J of trump can call 50, 40, or 20
            isValidCall = true;
          } else if (maxJordhiValue === 40 && (jordhiOption === 40 || jordhiOption === 20)) {
            // K+Q of trump can call 40 or 20
            isValidCall = true;
          } else if (maxJordhiValue === 30 && (jordhiOption === 30 || jordhiOption === 20)) {
            // K+Q+J of non-trump can call 30 or 20
            isValidCall = true;
          } else if (maxJordhiValue === 20 && jordhiOption === 20) {
            // K+Q of non-trump can call 20
            isValidCall = true;
          }

          if (isValidCall) {
            isValidJordhi = true;
            jordhiSuit = suit;

            // Add the cards to the jordhiCards array
            const king = suitCards.find(card => card.value === 'K');
            const queen = suitCards.find(card => card.value === 'Q');
            jordhiCards = [king, queen];

            if (hasJack) {
              const jack = suitCards.find(card => card.value === 'J');
              jordhiCards.push(jack);
            }

            break;
          }
        }
      }

      // Log the result based on both card validity and hand count validity
      const isValidCards = isValidJordhi;
      const isFullyValid = isValidCards && isValidHandCount;

      if (isFullyValid) {
        console.log(`Player ${player.name} (Team ${playerTeam}) called a valid Jordhi ${jordhiOption} with ${jordhiCards.length} cards in ${jordhiSuit}`);
      } else if (isValidCards) {
        console.log(`Player ${player.name} (Team ${playerTeam}) called a Jordhi ${jordhiOption} with valid cards but invalid hand count (${handsWonByTeam} hands won)`);
      } else {
        console.log(`Player ${player.name} (Team ${playerTeam}) called an invalid Jordhi ${jordhiOption} - doesn't have the required card combination`);
      }

      // Store the jordhi call
      if (!lobby.jordhiCalls) {
        lobby.jordhiCalls = [];
        if (matchedLobby) matchedLobby.jordhiCalls = [];
      }

      const jordhiCall = {
        playerId: socket.id,
        playerName: player.name,
        playerTeam: playerTeam,
        value: jordhiOption,
        handNumber: handsWonByTeam,
        isValidCards: isValidCards,
        isValidHandCount: isValidHandCount,
        isFullyValid: isFullyValid,
        jordhiSuit: jordhiSuit,
        jordhiCards: isValidCards ? jordhiCards.map(card => ({ suit: card.suit, value: card.value })) : []
      };

      // Add to the global Jordhi calls list
      lobby.jordhiCalls.push(jordhiCall);
      if (matchedLobby) matchedLobby.jordhiCalls.push(jordhiCall);

      // Add to the player's personal Jordhi calls list to track which suits they've used
      lobby.playerJordhiCalls[socket.id].push({
        suit: jordhiSuit,
        value: jordhiOption,
        isValidCards: isValidCards,
        isValidHandCount: isValidHandCount,
        isFullyValid: isFullyValid
      });

      // If this is a valid Jordhi call, update the matched lobby's player Jordhi calls as well
      if (matchedLobby && matchedLobby !== lobby) {
        if (!matchedLobby.playerJordhiCalls) {
          matchedLobby.playerJordhiCalls = {};
        }

        if (!matchedLobby.playerJordhiCalls[socket.id]) {
          matchedLobby.playerJordhiCalls[socket.id] = [];
        }

        matchedLobby.playerJordhiCalls[socket.id].push({
          suit: jordhiSuit,
          value: jordhiOption,
          isValidCards: isValidCards,
          isValidHandCount: isValidHandCount,
          isFullyValid: isFullyValid
        });
      }

      // Update the target scores based on the jordhi call (regardless of validity)
      // If the trumper team calls jordhi, add to the non-trumper team's target
      // If the non-trumper team calls jordhi, subtract from their target
      const trumperTeam = lobby.trumperTeam || null;

      // Make sure target scores are initialized
      if (!lobby.targetScores) {
        // Initialize target scores based on bidding
        const initialTarget = 105;
        const bidAmount = lobby.finalBid || 0;

        // If there was a bid, the non-trumper team's target is reduced by the bid amount
        if (trumperTeam && bidAmount > 0) {
          const nonTrumperTeam = trumperTeam === 1 ? 2 : 1;
          lobby.targetScores = {
            team1: trumperTeam === 1 ? initialTarget : initialTarget - bidAmount,
            team2: trumperTeam === 2 ? initialTarget : initialTarget - bidAmount
          };

          console.log(`Initializing target scores with bid ${bidAmount}. Trumper team: ${trumperTeam}, Non-trumper team: ${nonTrumperTeam}`);
          console.log(`Initial target scores: Team 1: ${lobby.targetScores.team1}, Team 2: ${lobby.targetScores.team2}`);
        } else {
          // No bid or no trumper team, use default targets
          lobby.targetScores = { team1: initialTarget, team2: initialTarget };
          console.log(`Initializing default target scores: Team 1: ${initialTarget}, Team 2: ${initialTarget}`);
        }

        if (matchedLobby) matchedLobby.targetScores = { ...lobby.targetScores };
      }

      // Adjust target scores for all Jordhi calls
      if (trumperTeam) {
        if (playerTeam === trumperTeam) {
          // Trumper team called jordhi, add to non-trumper team's target
          const nonTrumperTeam = trumperTeam === 1 ? 2 : 1;

          // Get current target score before adjustment
          const currentTarget = lobby.targetScores[`team${nonTrumperTeam}`];

          // Add jordhi value to non-trumper team's target
          lobby.targetScores[`team${nonTrumperTeam}`] += jordhiOption;
          if (matchedLobby) matchedLobby.targetScores[`team${nonTrumperTeam}`] += jordhiOption;

          if (isFullyValid) {
            console.log(`Trumper team (Team ${trumperTeam}) called a valid Jordhi ${jordhiOption}. Non-trumper team (Team ${nonTrumperTeam}) target increased from ${currentTarget} to ${lobby.targetScores[`team${nonTrumperTeam}`]}`);
          } else if (isValidCards) {
            console.log(`Trumper team (Team ${trumperTeam}) called a Jordhi ${jordhiOption} with valid cards but invalid hand count. Non-trumper team (Team ${nonTrumperTeam}) target increased from ${currentTarget} to ${lobby.targetScores[`team${nonTrumperTeam}`]}`);
          } else {
            console.log(`Trumper team (Team ${trumperTeam}) called an invalid Jordhi ${jordhiOption}, but target scores still adjusted. Non-trumper team (Team ${nonTrumperTeam}) target increased from ${currentTarget} to ${lobby.targetScores[`team${nonTrumperTeam}`]}`);
          }
        } else {
          // Non-trumper team called jordhi, subtract from their target
          // Get current target score before adjustment
          const currentTarget = lobby.targetScores[`team${playerTeam}`];

          // Subtract jordhi value from non-trumper team's target
          lobby.targetScores[`team${playerTeam}`] -= jordhiOption;
          if (matchedLobby) matchedLobby.targetScores[`team${playerTeam}`] -= jordhiOption;

          if (isFullyValid) {
            console.log(`Non-trumper team (Team ${playerTeam}) called a valid Jordhi ${jordhiOption}. Their target decreased from ${currentTarget} to ${lobby.targetScores[`team${playerTeam}`]}`);
          } else if (isValidCards) {
            console.log(`Non-trumper team (Team ${playerTeam}) called a Jordhi ${jordhiOption} with valid cards but invalid hand count. Their target decreased from ${currentTarget} to ${lobby.targetScores[`team${playerTeam}`]}`);
          } else {
            console.log(`Non-trumper team (Team ${playerTeam}) called an invalid Jordhi ${jordhiOption}, but target scores still adjusted. Their target decreased from ${currentTarget} to ${lobby.targetScores[`team${playerTeam}`]}`);
          }
        }
      } else {
        // If trumper team is not set, try to find it
        const trumpSelectorId = lobby.trumpSelectorId;
        if (trumpSelectorId) {
          // Try to find the trump selector in the players array first
          let trumpSelectorPlayer = null;

          if (Array.isArray(lobby.players)) {
            trumpSelectorPlayer = lobby.players.find(p => p.id === trumpSelectorId);
          }

          // If not found in players array, try to find in teams structure
          if (!trumpSelectorPlayer && lobby.teams) {
            for (const teamId of [1, 2]) {
              if (lobby.teams[teamId]) {
                const foundPlayer = lobby.teams[teamId].find(p => p.id === trumpSelectorId);
                if (foundPlayer) {
                  trumpSelectorPlayer = foundPlayer;
                  break;
                }
              }
            }
          }

          if (trumpSelectorPlayer) {
            lobby.trumperTeam = trumpSelectorPlayer.team;
            if (matchedLobby) matchedLobby.trumperTeam = trumpSelectorPlayer.team;
            console.log(`Retroactively set trumper team to ${trumpSelectorPlayer.team}`);

            // Now that we have the trumper team, adjust the target scores
            if (playerTeam === trumpSelectorPlayer.team) {
              // Trumper team called jordhi, add to non-trumper team's target
              const nonTrumperTeam = trumpSelectorPlayer.team === 1 ? 2 : 1;

              // Get current target score before adjustment
              const currentTarget = lobby.targetScores[`team${nonTrumperTeam}`];

              // Add jordhi value to non-trumper team's target
              lobby.targetScores[`team${nonTrumperTeam}`] += jordhiOption;
              if (matchedLobby) matchedLobby.targetScores[`team${nonTrumperTeam}`] += jordhiOption;

              if (isFullyValid) {
                console.log(`Trumper team (Team ${trumpSelectorPlayer.team}) called a valid Jordhi ${jordhiOption}. Non-trumper team (Team ${nonTrumperTeam}) target increased from ${currentTarget} to ${lobby.targetScores[`team${nonTrumperTeam}`]}`);
              } else if (isValidCards) {
                console.log(`Trumper team (Team ${trumpSelectorPlayer.team}) called a Jordhi ${jordhiOption} with valid cards but invalid hand count. Non-trumper team (Team ${nonTrumperTeam}) target increased from ${currentTarget} to ${lobby.targetScores[`team${nonTrumperTeam}`]}`);
              } else {
                console.log(`Trumper team (Team ${trumpSelectorPlayer.team}) called an invalid Jordhi ${jordhiOption}, but target scores still adjusted. Non-trumper team (Team ${nonTrumperTeam}) target increased from ${currentTarget} to ${lobby.targetScores[`team${nonTrumperTeam}`]}`);
              }
            } else {
              // Non-trumper team called jordhi, subtract from their target
              // Get current target score before adjustment
              const currentTarget = lobby.targetScores[`team${playerTeam}`];

              // Subtract jordhi value from non-trumper team's target
              lobby.targetScores[`team${playerTeam}`] -= jordhiOption;
              if (matchedLobby) matchedLobby.targetScores[`team${playerTeam}`] -= jordhiOption;

              if (isFullyValid) {
                console.log(`Non-trumper team (Team ${playerTeam}) called a valid Jordhi ${jordhiOption}. Their target decreased from ${currentTarget} to ${lobby.targetScores[`team${playerTeam}`]}`);
              } else if (isValidCards) {
                console.log(`Non-trumper team (Team ${playerTeam}) called a Jordhi ${jordhiOption} with valid cards but invalid hand count. Their target decreased from ${currentTarget} to ${lobby.targetScores[`team${playerTeam}`]}`);
              } else {
                console.log(`Non-trumper team (Team ${playerTeam}) called an invalid Jordhi ${jordhiOption}, but target scores still adjusted. Their target decreased from ${currentTarget} to ${lobby.targetScores[`team${playerTeam}`]}`);
              }
            }
          } else {
            console.log(`Could not find trump selector player with ID ${trumpSelectorId}`);
          }
        } else {
          console.log(`No trumper team set and no trump selector ID found, cannot adjust target scores for Jordhi call`);
        }
      }

      // Send detailed information only to the player who made the call
      socket.emit('jordhi_called', {
        playerId: socket.id,
        playerName: player.name,
        playerTeam: playerTeam,
        value: jordhiOption,
        handNumber: handsWonByTeam, // Include the hand number
        targetScores: lobby.targetScores || { team1: 105, team2: 105 },
        isValidCards: isValidCards,
        isValidHandCount: isValidHandCount,
        isFullyValid: isFullyValid,
        jordhiSuit: jordhiSuit,
        jordhiCards: isValidCards ? jordhiCards.map(card => ({ suit: card.suit, value: card.value })) : []
      });

      // Send information to other players, including validity information
      socket.to(lobbyCode).emit('jordhi_called_by_other', {
        playerId: socket.id,
        playerName: player.name,
        playerTeam: playerTeam,
        value: jordhiOption,
        handNumber: handsWonByTeam, // Include the hand number
        targetScores: lobby.targetScores || { team1: 105, team2: 105 },
        // Send validity information to all players
        isValidCards: isValidCards,
        isValidHandCount: isValidHandCount,
        isFullyValid: isFullyValid,
        jordhiSuit: jordhiSuit
      });

      // Send a simple notification to all players about the Jordhi call
      io.to(lobbyCode).emit('jordhi_toast_notification', {
        playerName: player.name,
        playerTeam: playerTeam,
        value: jordhiOption
      });

      if (matchedLobby && matchedLobby.lobbyCode !== lobbyCode) {
        socket.to(matchedLobby.lobbyCode).emit('jordhi_called_by_other', {
          playerId: socket.id,
          playerName: player.name,
          playerTeam: playerTeam,
          value: jordhiOption,
          handNumber: handsWonByTeam, // Include the hand number
          targetScores: lobby.targetScores || { team1: 105, team2: 105 },
          // Send validity information to all players
          isValidCards: isValidCards,
          isValidHandCount: isValidHandCount,
          isFullyValid: isFullyValid,
          jordhiSuit: jordhiSuit
        });

        // Send the toast notification to the matched lobby as well
        io.to(matchedLobby.lobbyCode).emit('jordhi_toast_notification', {
          playerName: player.name,
          playerTeam: playerTeam,
          value: jordhiOption
        });
      }

      // If the Jordhi call is invalid (cards or hand count), send an error notification to the player
      if (!isFullyValid) {
        // Check if the player has already made a valid Jordhi call with this suit
        // We only care about card validity here, not hand count validity
        const alreadyCalledSuits = playerPreviousCalls
          .filter(call => call.isValidCards)
          .map(call => call.suit);

        let errorMessage = '';

        if (!isValidCards) {
          if (alreadyCalledSuits.includes(jordhiSuit)) {
            errorMessage = `Invalid Jordhi call: You've already made a valid Jordhi call with the ${jordhiSuit} suit.`;
          } else {
            errorMessage = `Invalid Jordhi call: You don't have the required card combination for a ${jordhiOption} Jordhi.`;
          }
        } else if (!isValidHandCount) {
          errorMessage = `Note: Your team has not won any hands yet. This Jordhi call is invalid, but will still affect target scores.`;
        }

        socket.emit('jordhi_notification', {
          message: errorMessage
        });
      }

      callback?.({ success: true });
    } catch (error) {
      console.error('Error in jordhi_selection:', error);
      callback?.({ success: false, error: error.message });
    }
  });

  // Handle reveal jordhi cards event
  socket.on('reveal_jordhi_cards', (data, callback) => {
    try {
      console.log('reveal_jordhi_cards event received from client:', socket.id, 'data:', data);

      // Get lobby code
      const lobbyCode = socketToLobby.get(socket.id);
      if (!lobbyCode || !lobbies.has(lobbyCode)) {
        console.error('Lobby not found for socket ID:', socket.id);
        return callback?.({ success: false, error: 'Lobby not found' });
      }

      const lobby = lobbies.get(lobbyCode);

      // Check if this is a single lobby with all players (invite code case)
      const team1Count = lobby.teams[1]?.length || 0;
      const team2Count = lobby.teams[2]?.length || 0;
      const totalPlayers = team1Count + team2Count;
      const isSingleLobby = totalPlayers === 4;

      let matchedLobbyCode;
      let matchedLobby;

      if (isSingleLobby) {
        // This is a single lobby with all players
        matchedLobby = lobby;
        matchedLobbyCode = lobbyCode;
      } else {
        // This is a matched lobby scenario
        matchedLobbyCode = lobby.matchedLobby;
        if (!matchedLobbyCode || !lobbies.has(matchedLobbyCode)) {
          console.error(`Matched lobby ${matchedLobbyCode} not found`);
          return callback?.({ success: false, error: 'Matched lobby not found' });
        }
        matchedLobby = lobbies.get(matchedLobbyCode);
      }

      // Use the handler to process the reveal
      const result = handleJordhiReveal(socket, data, lobby, matchedLobby, io);

      if (!result.success) {
        console.error('Error revealing Jordhi cards:', result.error);
        return callback?.({ success: false, error: result.error });
      }

      callback?.({ success: true });
    } catch (error) {
      console.error('Error in reveal_jordhi_cards:', error);
      callback?.({ success: false, error: error.message });
    }
  });

  // Generic game action handler for other actions
  socket.on('game_action', (data, callback) => {
    try {
      // Get lobby code
      const lobbyCode = socketToLobby.get(socket.id);
      if (!lobbyCode || !lobbies.has(lobbyCode)) {
        return callback?.({ success: false, error: 'Lobby not found' });
      }

      // Handle timeframe voting
      if (data.action === 'vote_timeframe') {
        try {
          console.log(`Received vote_timeframe action from ${socket.id}:`, data);

          const lobby = lobbies.get(lobbyCode);

          // Check if this is a single lobby with all players (invite code case)
          const team1Count = lobby.teams[1]?.length || 0;
          const team2Count = lobby.teams[2]?.length || 0;
          const totalPlayers = team1Count + team2Count;
          const isSingleLobby = totalPlayers === 4;

          console.log(`Lobby ${lobbyCode} has ${team1Count} players in team 1 and ${team2Count} players in team 2`);

          // If this is a single lobby, use the same lobby as the matched lobby
          const matchedLobby = isSingleLobby ? lobby : lobbies.get(lobby.matchedLobby);

          if (!matchedLobby) {
            console.error(`Matched lobby ${lobby.matchedLobby} not found, using current lobby as fallback`);
            // Continue with the current lobby as a fallback
          }

          // Find the player
          const player = lobby.players.find(p => p.id === socket.id);
          if (!player) {
            console.error(`Player ${socket.id} not found in lobby ${lobbyCode}`);
            return callback?.({ success: false, error: 'Player not found' });
          }

          // Initialize timeframe voting if needed
          timeframeUtils.initTimeframeVoting(lobby);
          if (matchedLobby && matchedLobby !== lobby) {
            timeframeUtils.initTimeframeVoting(matchedLobby);
          }

          // Record the vote
          const timeframe = parseInt(data.timeframe);
          timeframeUtils.recordTimeframeVote(lobby, socket.id, timeframe);

          // If this is a matched lobby scenario, sync the votes
          if (matchedLobby && matchedLobby !== lobby) {
            matchedLobby.timeframeVoting = JSON.parse(JSON.stringify(lobby.timeframeVoting));
          }

          // Notify all players that a vote has been received
          io.to(lobbyCode).emit('timeframe_vote_received', {
            playerId: socket.id,
            playerName: player.name,
            timeframe: timeframe
          });

          // If matched lobby exists and is different, notify it too
          if (matchedLobby && matchedLobby.lobbyCode !== lobbyCode) {
            io.to(matchedLobby.lobbyCode).emit('timeframe_vote_received', {
              playerId: socket.id,
              playerName: player.name,
              timeframe: timeframe
            });
          }

          console.log(`Player ${player.name} (${socket.id}) voted for timeframe: ${timeframe} seconds`);

          // Check if all players have voted
          if (timeframeUtils.allPlayersVoted(lobby)) {
            // Determine the selected timeframe
            const selectedTimeframe = timeframeUtils.determineSelectedTimeframe(lobby);
            const votingState = timeframeUtils.getVotingState(lobby);

            console.log(`All players have voted. Selected timeframe: ${selectedTimeframe} seconds`);
            console.log(`Voting results:`, votingState.results);

            // Sync the selected timeframe to the matched lobby
            if (matchedLobby && matchedLobby !== lobby) {
              matchedLobby.timeframeVoting.selectedTimeframe = selectedTimeframe;
              matchedLobby.timeframeVoting.votingComplete = true;
            }

            // Notify all players of the results
            io.to(lobbyCode).emit('timeframe_vote_results', {
              results: votingState.results,
              selectedTimeframe: selectedTimeframe
            });

            // If matched lobby exists and is different, notify it too
            if (matchedLobby && matchedLobby.lobbyCode !== lobbyCode) {
              io.to(matchedLobby.lobbyCode).emit('timeframe_vote_results', {
                results: votingState.results,
                selectedTimeframe: selectedTimeframe
              });
            }

            // After a delay, transition to dealer determination phase
            setTimeout(() => {
              io.to(lobbyCode).emit('game_phase_updated', {
                phase: 'dealer-determination',
                players: lobby.players
              });

              // If matched lobby exists and is different, notify it too
              if (matchedLobby && matchedLobby.lobbyCode !== lobbyCode) {
                io.to(matchedLobby.lobbyCode).emit('game_phase_updated', {
                  phase: 'dealer-determination',
                  players: lobby.players
                });
              }

              console.log(`Game phase updated to dealer-determination for lobby ${lobbyCode}`);
            }, 3000); // 3 second delay
          }

          return callback?.({ success: true });
        } catch (error) {
          console.error('Error handling timeframe vote:', error);
          return callback?.({ success: false, error: error.message });
        }
      }
      // Handle chat messages
      else if (data.action === 'send_chat_message' && data.message) {
        const lobby = lobbies.get(lobbyCode);

        if (!lobby) {
          console.error(`Lobby ${lobbyCode} not found for chat message`);
          return callback?.({ success: false, error: 'Lobby not found' });
        }

        // Find the player who sent the message
        const player = lobby.players.find(p => p.id === socket.id);

        if (player) {
          // Create the message object with the player's name
          const message = {
            ...data.message,
            senderName: player.name
          };

          console.log(`Chat message from ${player.name}: ${message.text}`);

          // Send the message to all players in the lobby
          io.to(lobbyCode).emit('chat_message', message);

          // If this lobby is matched with another lobby, send the message there too
          if (lobby.matchedLobby) {
            const matchedLobbyCode = lobby.matchedLobby;
            if (lobbies.has(matchedLobbyCode)) {
              console.log(`Forwarding chat message to matched lobby ${matchedLobbyCode}`);
              io.to(matchedLobbyCode).emit('chat_message', message);
            }
          }
        } else {
          console.error(`Player ${socket.id} not found in lobby ${lobbyCode}`);
        }
      }
      // Handle video call actions
      else if (data.action === 'join_video_call') {
        const lobby = lobbies.get(lobbyCode);

        if (!lobby) {
          console.error(`Lobby ${lobbyCode} not found for video call`);
          return callback?.({ success: false, error: 'Lobby not found' });
        }

        // Find the player who is joining the call
        const player = lobby.players.find(p => p.id === socket.id);

        if (player) {
          console.log(`Player ${player.name} joined video call in lobby ${lobbyCode}`);

          // Initialize video call users array if it doesn't exist
          if (!lobby.videoCallUsers) {
            lobby.videoCallUsers = [];
          }

          // Add player to video call users if not already there
          if (!lobby.videoCallUsers.includes(socket.id)) {
            lobby.videoCallUsers.push(socket.id);
          }

          // Get all users in the video call from both lobbies
          const allVideoUsers = [...lobby.videoCallUsers];

          // Add users from matched lobby if it exists
          if (lobby.matchedLobby) {
            const matchedLobby = lobbies.get(lobby.matchedLobby);
            if (matchedLobby && matchedLobby.videoCallUsers) {
              allVideoUsers.push(...matchedLobby.videoCallUsers);
            }
          }

          // Get player info for all users in the call
          const videoUsers = allVideoUsers
            .map(userId => {
              // Find player in this lobby
              let userPlayer = lobby.players.find(p => p.id === userId);

              // If not found, check matched lobby
              if (!userPlayer && lobby.matchedLobby) {
                const matchedLobby = lobbies.get(lobby.matchedLobby);
                if (matchedLobby) {
                  userPlayer = matchedLobby.players.find(p => p.id === userId);
                }
              }

              return userPlayer ? { id: userId, name: userPlayer.name } : null;
            })
            .filter(user => user !== null);

          // Notify the joining player about all users in the call
          socket.emit('video_users', { users: videoUsers });

          // Notify all other users about the new user
          socket.to(lobbyCode).emit('video_user_joined', {
            peerId: socket.id,
            peerName: player.name
          });

          // Notify users in matched lobby if it exists
          if (lobby.matchedLobby) {
            const matchedLobbyCode = lobby.matchedLobby;
            if (lobbies.has(matchedLobbyCode)) {
              socket.to(matchedLobbyCode).emit('video_user_joined', {
                peerId: socket.id,
                peerName: player.name
              });
            }
          }
        } else {
          console.error(`Player ${socket.id} not found in lobby ${lobbyCode}`);
        }
      }
      else if (data.action === 'leave_video_call') {
        const lobby = lobbies.get(lobbyCode);

        if (!lobby) {
          console.error(`Lobby ${lobbyCode} not found for video call`);
          return callback?.({ success: false, error: 'Lobby not found' });
        }

        // Find the player who is leaving the call
        const player = lobby.players.find(p => p.id === socket.id);

        if (player) {
          console.log(`Player ${player.name} left video call in lobby ${lobbyCode}`);

          // Remove player from video call users
          if (lobby.videoCallUsers) {
            lobby.videoCallUsers = lobby.videoCallUsers.filter(id => id !== socket.id);
          }

          // Notify all other users that this user left
          socket.to(lobbyCode).emit('video_user_left', { peerId: socket.id });

          // Notify users in matched lobby if it exists
          if (lobby.matchedLobby) {
            const matchedLobbyCode = lobby.matchedLobby;
            if (lobbies.has(matchedLobbyCode)) {
              socket.to(matchedLobbyCode).emit('video_user_left', { peerId: socket.id });
            }
          }
        } else {
          console.error(`Player ${socket.id} not found in lobby ${lobbyCode}`);
        }
      }
      else if (data.action === 'video_signal' && data.peerId && data.signal) {
        const lobby = lobbies.get(lobbyCode);

        if (!lobby) {
          console.error(`Lobby ${lobbyCode} not found for video signal`);
          return callback?.({ success: false, error: 'Lobby not found' });
        }

        // Find the player who is sending the signal
        const player = lobby.players.find(p => p.id === socket.id);

        if (player) {
          console.log(`Video signal from ${player.name} to peer ${data.peerId}`);

          // Forward the signal to the specific peer
          const targetSocket = io.sockets.sockets.get(data.peerId);
          if (targetSocket) {
            targetSocket.emit('video_signal', {
              peerId: socket.id,
              signal: data.signal
            });
          } else {
            console.error(`Target socket ${data.peerId} not found`);
          }
        } else {
          console.error(`Player ${socket.id} not found in lobby ${lobbyCode}`);
        }
      }
      else {
        // Forward other actions to all players in the lobby
        io.to(lobbyCode).emit('game_action', { ...data, playerId: socket.id });
      }

      callback?.({ success: true });
    } catch (error) {
      console.error('Error handling game action:', error);
      callback?.({ success: false, error: error.message });
    }
  });

  // Start dealer determination process
  socket.on('start_dealer_determination', (data, callback) => {
    try {
      let { lobbyCode } = data;

      // Check if lobby exists
      if (!lobbyCode && socketToLobby.has(socket.id)) {
        lobbyCode = socketToLobby.get(socket.id);
      }

      if (!lobbies.has(lobbyCode)) {
        return callback?.({ success: false, error: 'Lobby not found' });
      }

      const lobby = lobbies.get(lobbyCode);
      let matchedLobby = null;
      let allPlayers = [];

      // Check if this is a single lobby with all players (invite code case)
      const team1Count = lobby.teams[1]?.length || 0;
      const team2Count = lobby.teams[2]?.length || 0;
      const totalPlayers = team1Count + team2Count;

      if (totalPlayers === 4) {
        console.log('Single lobby with all 4 players detected (invite code case)');
        // This is a single lobby with all 4 players (invite code case)
        // We'll use the same lobby for both lobby and matchedLobby
        matchedLobby = lobby;

        // Get all players from this single lobby
        if (lobby.teams[1] && lobby.teams[1].length > 0) {
          allPlayers = [...allPlayers, ...lobby.teams[1]];
        }
        if (lobby.teams[2] && lobby.teams[2].length > 0) {
          allPlayers = [...allPlayers, ...lobby.teams[2]];
        }
      } else {
        // This is the matched lobbies case (find match case)
        // Get the matched lobby
        if (!lobby.matchedLobby) {
          return callback?.({ success: false, error: 'Not matched with another team' });
        }

        matchedLobby = lobbies.get(lobby.matchedLobby);
        if (!matchedLobby) {
          return callback?.({ success: false, error: 'Matched lobby not found' });
        }
      }

      // Check if dealer determination is already in progress
      if (lobby.dealerDeterminationInProgress || matchedLobby.dealerDeterminationInProgress) {
        console.log('Dealer determination already in progress');
        return callback?.({ success: false, error: 'Dealer determination already in progress' });
      }

      // Mark both lobbies as having dealer determination in progress
      lobby.dealerDeterminationInProgress = true;
      matchedLobby.dealerDeterminationInProgress = true;

      // If we don't have allPlayers yet (matched lobbies case), collect them now
      if (allPlayers.length === 0) {
        // Add players from team 1 in both lobbies
        if (lobby.teams[1] && lobby.teams[1].length > 0) {
          allPlayers = [...allPlayers, ...lobby.teams[1]];
        }

        // Add players from team 2 in both lobbies
        if (lobby.teams[2] && lobby.teams[2].length > 0) {
          allPlayers = [...allPlayers, ...lobby.teams[2]];
        }

        // Add players from matched lobby team 1 (if different from lobby)
        if (matchedLobby !== lobby && matchedLobby.teams[1] && matchedLobby.teams[1].length > 0) {
          allPlayers = [...allPlayers, ...matchedLobby.teams[1]];
        }

        // Add players from matched lobby team 2 (if different from lobby)
        if (matchedLobby !== lobby && matchedLobby.teams[2] && matchedLobby.teams[2].length > 0) {
          allPlayers = [...allPlayers, ...matchedLobby.teams[2]];
        }
      }

      // Ensure we have exactly 4 players
      if (allPlayers.length !== 4) {
        console.error(`Expected 4 players but got ${allPlayers.length}. Lobby teams:`,
          Object.keys(lobby.teams).map(team => `Team ${team}: ${lobby.teams[team].length} players`),
          `Matched lobby teams:`,
          Object.keys(matchedLobby.teams).map(team => `Team ${team}: ${matchedLobby.teams[team].length} players`));

        // If we don't have 4 players, try to fix the teams
        if (allPlayers.length < 4) {
          return callback?.({ success: false, error: 'Not enough players to start dealer determination. Need exactly 4 players.' });
        }

        // If we have more than 4 players, take the first 4
        if (allPlayers.length > 4) {
          console.warn('Too many players, taking the first 4');
          allPlayers = allPlayers.slice(0, 4);
        }
      }

      // Sort by ID for consistent order across clients
      allPlayers = allPlayers.sort((a, b) => a.id.localeCompare(b.id));

      // Ensure we have exactly 4 players
      if (allPlayers.length !== 4) {
        console.error(`Expected 4 players but got ${allPlayers.length}. Cannot start dealer determination.`);
        return callback?.({ success: false, error: 'Need exactly 4 players to start dealer determination.' });
      }

      console.log('All players for dealer determination:',
        allPlayers.map(p => `${p.name} (${p.id}) - Team ${p.team}`));

      // Create a shuffled deck
      const deck = createShuffledDeck();

      // Choose a random starting player index (0-3)
      const startingPlayerIndex = getRandomInt(allPlayers.length);
      console.log(`Starting dealer determination with player index ${startingPlayerIndex}: ${allPlayers[startingPlayerIndex].name}`);

      // Store the deck and current player index in the lobby
      lobby.dealerDeck = deck;
      lobby.currentDealerPlayerIndex = startingPlayerIndex;
      lobby.dealerFound = false;
      lobby.dealerId = null;

      // Initialize cards dealt counter for each player
      lobby.cardsDealtPerPlayer = {};
      allPlayers.forEach(player => {
        lobby.cardsDealtPerPlayer[player.id] = 0;
      });

      // Share the same deck with matched lobby
      matchedLobby.dealerDeck = deck;
      matchedLobby.currentDealerPlayerIndex = startingPlayerIndex;
      matchedLobby.dealerFound = false;
      matchedLobby.dealerId = null;

      // Share the cards dealt counter with matched lobby
      matchedLobby.cardsDealtPerPlayer = {...lobby.cardsDealtPerPlayer};

      // Broadcast to all players in both lobbies that dealer determination is starting
      // Make sure all clients have the same player list
      const dealerDeterminationData = {
        players: allPlayers,
        currentPlayerIndex: startingPlayerIndex
      };

      console.log('Broadcasting dealer determination started with players:',
        allPlayers.map(p => `${p.name} (${p.id}) - Team ${p.team}`));

      // First, reset any existing dealer determination state
      io.to(lobbyCode).emit('reset_dealer_determination');
      io.to(matchedLobby.lobbyCode).emit('reset_dealer_determination');

      // After a short delay, send the dealer determination data
      setTimeout(() => {
        // Verify all players are still connected before proceeding
        const connectedSockets = io.sockets.adapter.rooms.get(lobbyCode);
        const matchedConnectedSockets = io.sockets.adapter.rooms.get(matchedLobby.lobbyCode);

        if (!connectedSockets || !matchedConnectedSockets ||
            connectedSockets.size < lobby.teams[1].length ||
            matchedConnectedSockets.size < matchedLobby.teams[1].length) {
          console.error('Not all players are connected, aborting dealer determination');
          lobby.dealerDeterminationInProgress = false;
          matchedLobby.dealerDeterminationInProgress = false;
          return callback?.({ success: false, error: 'Not all players are connected' });
        }

        // Send the dealer determination data to all clients
        io.to(lobbyCode).emit('dealer_determination_started', dealerDeterminationData);
        io.to(matchedLobby.lobbyCode).emit('dealer_determination_started', dealerDeterminationData);

        // Start the dealer determination process after another short delay
        setTimeout(() => {
          dealNextCard(lobby, matchedLobby, allPlayers);
        }, 1000);
      }, 1000);

      callback?.({ success: true });
    } catch (error) {
      console.error('Error starting dealer determination:', error);
      callback?.({ success: false, error: 'Failed to start dealer determination' });
    }
  });

  // Set dealer and trump selector
  socket.on('set_dealer', (data, callback) => {
    try {
      const { dealerId, trumpSelectorId } = data;
      let { lobbyCode } = data;

      // Check if lobby exists
      if (!lobbyCode && socketToLobby.has(socket.id)) {
        lobbyCode = socketToLobby.get(socket.id);
      }

      if (!lobbies.has(lobbyCode)) {
        return callback?.({ success: false, error: 'Lobby not found' });
      }

      const lobby = lobbies.get(lobbyCode);
      let matchedLobby = null;
      let allPlayers = [];

      // Check if this is a single lobby with all players (invite code case)
      const team1Count = lobby.teams[1]?.length || 0;
      const team2Count = lobby.teams[2]?.length || 0;
      const totalPlayers = team1Count + team2Count;

      if (totalPlayers === 4) {
        console.log('Single lobby with all 4 players detected (invite code case)');
        // This is a single lobby with all 4 players (invite code case)
        // We'll use the same lobby for both lobby and matchedLobby
        matchedLobby = lobby;

        // Get all players from this single lobby
        if (lobby.teams[1] && lobby.teams[1].length > 0) {
          allPlayers = [...allPlayers, ...lobby.teams[1]];
        }
        if (lobby.teams[2] && lobby.teams[2].length > 0) {
          allPlayers = [...allPlayers, ...lobby.teams[2]];
        }
      } else {
        // This is the matched lobbies case (find match case)
        // Get the matched lobby
        if (!lobby.matchedLobby) {
          return callback?.({ success: false, error: 'Not matched with another team' });
        }

        matchedLobby = lobbies.get(lobby.matchedLobby);
        if (!matchedLobby) {
          return callback?.({ success: false, error: 'Matched lobby not found' });
        }

        // Get all players from both lobbies
        allPlayers = [...lobby.teams[1], ...matchedLobby.teams[1]];
      }
      const dealerExists = allPlayers.some(p => p.id === dealerId);
      const trumpSelectorExists = allPlayers.some(p => p.id === trumpSelectorId);

      if (!dealerExists || !trumpSelectorExists) {
        return callback?.({ success: false, error: 'Invalid dealer or trump selector' });
      }

      console.log(`Setting dealer to ${dealerId} and trump selector to ${trumpSelectorId}`);

      // Broadcast to all players in both lobbies
      io.to(lobbyCode).emit('dealer_updated', { dealerId, trumpSelectorId });
      io.to(matchedLobby.lobbyCode).emit('dealer_updated', { dealerId, trumpSelectorId });

      callback?.({ success: true });
    } catch (error) {
      console.error('Error setting dealer:', error);
      callback?.({ success: false, error: 'Failed to set dealer' });
    }
  });

  // Handle four_eight_ball_selection event
  socket.on('four_eight_ball_selection', (data, callback) => {
    try {
      console.log('four_eight_ball_selection event received from client:', socket.id, 'data:', data);

      // Get lobby code
      const lobbyCode = socketToLobby.get(socket.id);
      if (!lobbyCode || !lobbies.has(lobbyCode)) {
        console.error('Lobby not found for socket ID:', socket.id);
        return callback?.({ success: false, error: 'Lobby not found' });
      }

      const lobby = lobbies.get(lobbyCode);

      // IMMEDIATELY stop any active timers when a 4-ball claim is made
      const turnUtils = require('./src/utils/server/turnUtils');
      turnUtils.stopTurnTimer(lobby);

      // Check if this is a single lobby with all players (invite code case)
      const team1Count = lobby.teams[1]?.length || 0;
      const team2Count = lobby.teams[2]?.length || 0;
      const totalPlayers = team1Count + team2Count;
      const isSingleLobby = totalPlayers === 4;

      // If this is a single lobby, use the same lobby as the matched lobby
      const matchedLobby = isSingleLobby ? lobby : lobbies.get(lobby.matchedLobby);

      if (!matchedLobby) {
        console.error(`Matched lobby ${lobby.matchedLobby} not found, using current lobby as fallback`);
        // Continue with the current lobby as a fallback
      }

      // Also stop timers for matched lobby if it's different
      if (matchedLobby && matchedLobby !== lobby) {
        turnUtils.stopTurnTimer(matchedLobby);
      }
      console.log('Stopped all timers immediately upon receiving 4-ball claim');

      // Find the player who made the selection
      const player = lobby.players.find(p => p.id === socket.id);
      if (!player) {
        console.error(`Player ${socket.id} not found in lobby ${lobbyCode}`);
        return callback?.({ success: false, error: 'Player not found' });
      }

      console.log(`Player ${player.name} (${player.id}) selected ${data.ballType} with option: ${data.option}`);

      // Handle "Called incorrect Jodhi" option
      if (data.option === "Called incorrect Jodhi" && data.jordhiCallPlayerId) {
        console.log(`Player ${player.name} (Team ${player.team}) is 4-balling Jordhi call by player ${data.jordhiCallPlayerId}`);

        // Find the Jordhi call
        const jordhiCalls = lobby.jordhiCalls || [];
        const jordhiCall = jordhiCalls.find(call => call.playerId === data.jordhiCallPlayerId);

        if (!jordhiCall) {
          console.error(`Jordhi call by player ${data.jordhiCallPlayerId} not found`);
          return callback?.({ success: false, error: 'Jordhi call not found' });
        }

        // Get the Jordhi caller's team
        const jordhiCallerTeam = jordhiCall.playerTeam;

        // The team that gets the 4 balls is the opposite of the Jordhi caller's team
        const winningTeam = jordhiCallerTeam === 1 ? 2 : 1;

        // Initialize ball scores if not already done
        if (!lobby.ballScores) {
          lobby.ballScores = { team1: 0, team2: 0 };
          matchedLobby.ballScores = { team1: 0, team2: 0 };
        }

        // Check if we're dealing with a single lobby case (all 4 players in one lobby)
        const isSingleLobby = (lobby === matchedLobby);
        console.log(`Is single lobby case: ${isSingleLobby}`);

        // Award 4 balls to the winning team
        lobby.ballScores[`team${winningTeam}`] = 4; // Set to exactly 4 balls, not increment

        // Only update matchedLobby if it's different from lobby
        if (!isSingleLobby) {
            matchedLobby.ballScores[`team${winningTeam}`] = 4;
        }

        // Reset the other team's score to 0
        lobby.ballScores[`team${jordhiCallerTeam}`] = 0;

        // Only update matchedLobby if it's different from lobby
        if (!isSingleLobby) {
            matchedLobby.ballScores[`team${jordhiCallerTeam}`] = 0;
        }

        console.log(`Awarded 4 balls to Team ${winningTeam} for incorrect Jordhi call by Team ${jordhiCallerTeam}`);

        // Notify all players in both lobbies
        io.to(lobbyCode).emit('four_ball_awarded', {
          ballType: data.ballType,
          option: data.option,
          jordhiCallPlayerId: data.jordhiCallPlayerId,
          jordhiCallerTeam,
          winningTeam,
          ballsAwarded: 4,
          ballScores: lobby.ballScores
        });

        if (matchedLobby && matchedLobby.lobbyCode !== lobbyCode) {
          io.to(matchedLobby.lobbyCode).emit('four_ball_awarded', {
            ballType: data.ballType,
            option: data.option,
            jordhiCallPlayerId: data.jordhiCallPlayerId,
            jordhiCallerTeam,
            winningTeam,
            ballsAwarded: 4,
            ballScores: lobby.ballScores
          });
        }

        // Reset for next ball
        resetUtils.resetBallState(lobby, matchedLobby);

        // Reset hand counter and points
        lobby.currentHandId = 0;
        matchedLobby.currentHandId = 0;
        lobby.ballPoints = { team1: 0, team2: 0 };
        matchedLobby.ballPoints = { team1: 0, team2: 0 };

        // Get the current ball ID
        const ballId = (lobby.currentBallId || 0) + 1;
        lobby.currentBallId = ballId;
        matchedLobby.currentBallId = ballId;

        // Send ball_completed event to both lobbies
        const ballCompletedData = {
          ballId,
          winner: winningTeam,
          points: {
            team1: 0,
            team2: 0
          },
          nextDealer: lobby.dealerId, // Keep the same dealer
          ballScores: lobby.ballScores,
          fourBallAwarded: true,
          fourBallOption: data.option,
          fourBallWinningTeam: winningTeam,
          ballsAwarded: 4
        };

        io.to(lobbyCode).emit('ball_completed', ballCompletedData);
        io.to(matchedLobby.lobbyCode).emit('ball_completed', ballCompletedData);

        // Reset game phase to shuffle for the next ball after a delay
        setTimeout(() => {
          io.to(lobbyCode).emit('game_phase_updated', { phase: 'shuffle' });
          io.to(matchedLobby.lobbyCode).emit('game_phase_updated', { phase: 'shuffle' });
        }, 15000); // 15 second delay before resetting
      }
      // Handle "Never follow suit" option
      else if (data.option === "Never follow suit" && data.accusedPlayerId && data.handNumber) {
        console.log(`Player ${player.name} (Team ${player.team}) is 4-balling player ${data.accusedPlayerId} for not following suit in hand #${data.handNumber}`);

        // followSuitHandler and fourBallHandler are already imported at the top

        // Validate the "Never follow suit" claim
        const validationResult = followSuitHandler.validateFollowSuitClaim(
          lobby,
          socket.id,
          data.accusedPlayerId,
          data.handNumber
        );

        // Process the 4-ball claim
        const result = fourBallHandler.processFourBallClaim(
          io,
          lobby,
          matchedLobby,
          lobbyCode,
          player,
          socket.id,
          data,
          validationResult,
          'never_follow_suit_result',
          resetUtils,
          callback
        );

        if (!result.success) {
          return callback?.(result);
        }
      }
      // Handle "Under chopped" option
      else if (data.option === "Under chopped" && data.accusedPlayerId && data.handNumber) {
        console.log(`Player ${player.name} (Team ${player.team}) is 4-balling player ${data.accusedPlayerId} for under chopping in hand #${data.handNumber}`);

        // underChoppedHandler and fourBallHandler are already imported at the top

        // Validate the "Under chopped" claim
        const validationResult = underChoppedHandler.validateUnderChoppedClaim(
          lobby,
          socket.id,
          data.accusedPlayerId,
          data.handNumber
        );

        // Process the 4-ball claim
        const result = fourBallHandler.processFourBallClaim(
          io,
          lobby,
          matchedLobby,
          lobbyCode,
          player,
          socket.id,
          data,
          validationResult,
          'under_chopped_result',
          resetUtils,
          callback
        );

        if (!result.success) {
          return callback?.(result);
        }
      }
      // Handle other options (implement as needed)
      else {
        console.log(`Unhandled option: ${data.option} for ball type: ${data.ballType}`);
      }

      callback?.({ success: true });
    } catch (error) {
      console.error('Error in four_eight_ball_selection:', error);
      callback?.({ success: false, error: error.message });
    }
  });

  // Handle disconnection
  socket.on('disconnect', () => {
    try {
      console.log(`User disconnected: ${socket.id}`);

      // Check if player was in a lobby
      if (socketToLobby.has(socket.id)) {
        const lobbyCode = socketToLobby.get(socket.id);
        const lobby = lobbies.get(lobbyCode);

        if (lobby) {
          // Find player
          const playerIndex = lobby.players.findIndex(p => p.id === socket.id);

          if (playerIndex !== -1) {
            const player = lobby.players[playerIndex];
            console.log(`Player ${player.name} left lobby ${lobbyCode}`);

            // Check if dealer determination is in progress
            if (lobby.dealerDeterminationInProgress) {
              console.log(`Player ${player.name} disconnected during dealer determination`);

              // Get the matched lobby
              const matchedLobby = lobby.matchedLobby ? lobbies.get(lobby.matchedLobby) : null;

              if (matchedLobby) {
                // Reset dealer determination flags
                lobby.dealerDeterminationInProgress = false;
                matchedLobby.dealerDeterminationInProgress = false;

                // Notify all clients that dealer determination has been aborted
                io.to(lobbyCode).emit('dealer_determination_aborted', { reason: 'A player disconnected' });
                io.to(matchedLobby.lobbyCode).emit('dealer_determination_aborted', { reason: 'A player disconnected' });
              }
            }

            // Check if player was in a video call
            if (lobby.videoCallUsers && lobby.videoCallUsers.includes(socket.id)) {
              console.log(`Player ${player.name} disconnected during video call`);

              // Remove player from video call users
              lobby.videoCallUsers = lobby.videoCallUsers.filter(id => id !== socket.id);

              // Notify all other users that this user left the video call
              io.to(lobbyCode).emit('video_user_left', { peerId: socket.id });

              // Notify users in matched lobby if it exists
              if (lobby.matchedLobby) {
                const matchedLobbyCode = lobby.matchedLobby;
                if (lobbies.has(matchedLobbyCode)) {
                  io.to(matchedLobbyCode).emit('video_user_left', { peerId: socket.id });
                }
              }
            }

            // Remove player from team
            const teamIndex = lobby.teams[player.team].findIndex(p => p.id === socket.id);
            if (teamIndex !== -1) {
              lobby.teams[player.team].splice(teamIndex, 1);
            }

            // Remove player from lobby
            lobby.players.splice(playerIndex, 1);

            // If lobby is empty, delete it
            if (lobby.players.length === 0) {
              lobbies.delete(lobbyCode);
              console.log(`Lobby ${lobbyCode} deleted (empty)`);
            } else {
              // If host left, assign new host
              if (player.isHost && lobby.players.length > 0) {
                lobby.players[0].isHost = true;
              }

              // Notify remaining players
              io.to(lobbyCode).emit('players_updated', { players: lobby.players });
              console.log(`Player ${player.name} left lobby ${lobbyCode}`);
            }
          }
        }

        // Remove socket-to-lobby mapping
        socketToLobby.delete(socket.id);
      }
    } catch (error) {
      console.error('Error handling disconnection:', error);
    }
  });
});

// Deal functions have been moved to dealingUtils.js

// Start server
const tryPort = (port) => {
  return new Promise((resolve, reject) => {
    server.once('error', (err) => {
      if (err.code === 'EADDRINUSE') {
        console.log(`Port ${port} is already in use, trying ${port + 1}...`);
        resolve(false);
      } else {
        reject(err);
      }
    });

    server.once('listening', () => {
      // Set the current port for the HTML page
      currentPort = port;
      console.log(`Server running on port ${port}`);
      resolve(true);
    });

    server.listen(port);
  });
};

const startServer = async () => {
  // Check if running under iisnode
  if (process.env.IISNODE_VERSION) {
    console.log('Running under iisnode, using named pipe instead of port');

    // When running under iisnode, use the named pipe provided by IIS
    const namedPipe = process.env.PORT;

    server.listen(namedPipe, () => {
      console.log(`Server running under iisnode on named pipe: ${namedPipe}`);
      console.log('iisnode version:', process.env.IISNODE_VERSION);
      currentPort = 80; // IIS will handle the actual port
    });

    return;
  }

  // Regular standalone server startup
  let port = process.env.PORT || 3001;
  let maxAttempts = 5;
  let success = false;

  while (!success && maxAttempts > 0) {
    try {
      success = await tryPort(port);
      if (!success) {
        port++;
        maxAttempts--;
      }
    } catch (error) {
      console.error('Error starting server:', error);
      process.exit(1);
    }
  }

  if (!success) {
    console.error(`Could not find an available port after ${5 - maxAttempts} attempts.`);
    process.exit(1);
  }

  // Update the SOCKET_SERVER_URL in the client
  console.log('\n======================================================');
  console.log(`IMPORTANT: Make sure your client is connecting to:`);
  console.log(`http://**************:${port}`);
  console.log('======================================================\n');
};

startServer();
