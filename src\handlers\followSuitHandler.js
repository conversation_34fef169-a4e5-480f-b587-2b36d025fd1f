/**
 * <PERSON><PERSON> for validating "Never follow suit" 4-ball claims
 */

/**
 * Validate a "Never follow suit" 4-ball claim
 * @param {Object} lobby - The game lobby
 * @param {String} accuserId - The ID of the player making the accusation
 * @param {String} accusedId - The ID of the player being accused
 * @param {Number} handNumber - The hand number where the alleged violation occurred
 * @returns {Object} The result of the validation
 */
function validateFollowSuitClaim(lobby, accuserId, accusedId, handNumber) {
  // Find the players involved
  const accuser = lobby.players.find(p => p.id === accuserId);
  const accused = lobby.players.find(p => p.id === accusedId);

  if (!accuser || !accused) {
    console.error(`Player not found: accuser=${accuserId}, accused=${accusedId}`);
    return {
      isValid: false,
      reason: 'Player not found',
      accuserTeam: accuser?.team || 1,
      accusedTeam: accused?.team || 2,
      winningTeam: accused?.team || 2, // If accused not found, default to their team winning
      ballsAwarded: 4
    };
  }

  const accuserTeam = accuser.team;
  const accusedTeam = accused.team;

  // Make sure we have hands data
  if (!lobby.hands || lobby.hands.length === 0) {
    console.error('No hands data available');
    return {
      isValid: false,
      reason: 'No hands data available',
      accuserTeam,
      accusedTeam,
      winningTeam: accusedTeam, // Accused team wins if no data available
      ballsAwarded: 4
    };
  }

  // Find the specific hand in question
  const hand = lobby.hands.find(h => h.id === handNumber);
  if (!hand) {
    console.error(`Hand #${handNumber} not found`);
    return {
      isValid: false,
      reason: `Hand #${handNumber} not found`,
      accuserTeam,
      accusedTeam,
      winningTeam: accusedTeam, // Accused team wins if hand not found
      ballsAwarded: 4
    };
  }

  // Check if we have the play data for the accused player
  const accusedPlay = hand.plays?.find(p => p.playerId === accusedId);
  if (!accusedPlay) {
    console.error(`No play data found for player ${accusedId} in hand #${handNumber}`);
    return {
      isValid: false,
      reason: 'No play data found for accused player',
      accuserTeam,
      accusedTeam,
      winningTeam: accusedTeam, // Accused team wins if no play data
      ballsAwarded: 4
    };
  }

  // Get the lead suit for this hand
  const leadSuit = hand.leadSuit;
  if (!leadSuit) {
    console.error(`No lead suit found for hand #${handNumber}`);
    return {
      isValid: false,
      reason: 'No lead suit information available',
      accuserTeam,
      accusedTeam,
      winningTeam: accusedTeam, // Accused team wins if no lead suit info
      ballsAwarded: 4
    };
  }

  // Get the card played by the accused
  const cardPlayed = accusedPlay.card;
  if (!cardPlayed || !cardPlayed.suit) {
    console.error(`No card data found for player ${accusedId} in hand #${handNumber}`);
    return {
      isValid: false,
      reason: 'No card data found for accused player',
      accuserTeam,
      accusedTeam,
      winningTeam: accusedTeam, // Accused team wins if no card data
      ballsAwarded: 4
    };
  }

  // Get the hand the accused had before playing
  let handBeforePlaying = accusedPlay.handBeforePlay || [];

  // If handBeforePlaying is empty, try to get it from the lobby's playerCards
  if (handBeforePlaying.length === 0 && lobby.playerCards && lobby.playerCards[accusedId]) {
    // Make a deep copy of the player's cards at the time of the 4-ball call
    handBeforePlaying = JSON.parse(JSON.stringify(lobby.playerCards[accusedId] || []));

    // Add the card that was played (since it's already removed from the player's hand)
    if (cardPlayed) {
      handBeforePlaying.push({
        suit: cardPlayed.suit,
        value: cardPlayed.value
      });
    }

    console.log(`Retrieved ${handBeforePlaying.length} cards from player ${accusedId}'s current hand for 4-ball validation`);
  }

  // Check if the accused had any cards of the lead suit
  const hadLeadSuit = handBeforePlaying.some(card => card.suit === leadSuit);

  // Check if the accused followed suit
  const followedSuit = cardPlayed.suit === leadSuit;

  // The accusation is valid if the player had the lead suit but didn't play it
  const isValid = hadLeadSuit && !followedSuit;

  // Determine the winning team based on the validation result
  const winningTeam = isValid ? accuserTeam : accusedTeam;

  // Collect all cards played in this hand by all players
  const selectedHandCards = [];

  // If hand.plays exists, use it
  if (hand.plays && Array.isArray(hand.plays)) {
    hand.plays.forEach(play => {
      // Find the player who played this card
      const player = lobby.players.find(p => p.id === play.playerId);
      if (player && play.card) {
        selectedHandCards.push({
          suit: play.card.suit,
          value: play.card.value,
          playedBy: play.playerId,
          playerName: player.name,
          playerTeam: player.team
        });
      }
    });
  }
  // If hand.plays doesn't exist, try to use hand.cards
  else if (hand.cards && Array.isArray(hand.cards)) {
    hand.cards.forEach(card => {
      if (card.playedBy) {
        // Find the player who played this card
        const player = lobby.players.find(p => p.id === card.playedBy);
        if (player) {
          selectedHandCards.push({
            suit: card.suit,
            value: card.value,
            playedBy: card.playedBy,
            playerName: player.name,
            playerTeam: player.team
          });
        }
      }
    });
  }

  return {
    isValid,
    followedSuit, // Add this property to indicate if the player followed suit
    hadLeadSuit,  // Add this property to indicate if the player had the lead suit
    reason: isValid
      ? `Player had ${leadSuit} but played ${cardPlayed.suit}`
      : followedSuit
        ? `Player correctly followed suit with ${cardPlayed.value} of ${cardPlayed.suit}`
        : `Player did not have ${leadSuit} to follow suit`,
    accuserTeam,
    accusedTeam,
    winningTeam,
    ballsAwarded: 4,
    leadSuit,
    cardPlayed,
    handBeforePlaying,
    selectedHandCards // Add the selected hand cards to the result
  };
}

export default {
  validateFollowSuitClaim
};
