// Simple test script to verify server configuration
import express from 'express';
import http from 'http';

const app = express();

// Simple test route
app.get('/', (req, res) => {
  res.json({
    message: 'Thunee server is running!',
    timestamp: new Date().toISOString(),
    environment: {
      isIISNode: !!process.env.IISNODE_VERSION,
      iisNodeVersion: process.env.IISNODE_VERSION || 'Not running under iisnode',
      port: process.env.PORT || 'No PORT environment variable',
      nodeVersion: process.version
    }
  });
});

// Test route for WebSocket compatibility
app.get('/test', (req, res) => {
  res.json({
    status: 'OK',
    message: 'Server is ready for WebSocket connections'
  });
});

const server = http.createServer(app);

// Check if running under iisnode
if (process.env.IISNODE_VERSION) {
  console.log('Running under iisnode, using named pipe');
  const namedPipe = process.env.PORT;

  server.listen(namedPipe, () => {
    console.log(`Test server running under iisnode on named pipe: ${namedPipe}`);
    console.log('iisnode version:', process.env.IISNODE_VERSION);
  });
} else {
  // Regular standalone server startup
  const port = process.env.PORT || 3001;

  server.listen(port, () => {
    console.log(`Test server running on port ${port}`);
    console.log('Visit http://localhost:' + port + ' to test');
  });
}

// Handle errors
server.on('error', (err) => {
  console.error('Server error:', err);
  if (err.code === 'EACCES') {
    console.error('Permission denied. This usually means:');
    console.error('1. The port is already in use');
    console.error('2. You need administrator privileges');
    console.error('3. The port is restricted (ports < 1024 require admin rights)');
  }
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('Shutting down server...');
  server.close(() => {
    console.log('Server shut down successfully');
    process.exit(0);
  });
});

process.on('SIGTERM', () => {
  console.log('Received SIGTERM, shutting down gracefully...');
  server.close(() => {
    console.log('Server shut down successfully');
    process.exit(0);
  });
});
