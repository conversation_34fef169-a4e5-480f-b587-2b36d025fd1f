# iisnode configuration file for Thunee Node.js application

# The main Node.js application entry point
nodeProcessCommandLine: node

# Enable logging for debugging
loggingEnabled: true
logDirectory: iisnode

# Performance settings
maxNamedPipeConnectionRetry: 3
namedPipeConnectionRetryDelay: 2000
maxNamedPipeConnectionPoolSize: 512
maxNamedPipePooledConnectionAge: 30000

# Request handling
initialRequestBufferSize: 4096
maxRequestBufferSize: 65536

# File watching for auto-restart
watchedFiles: *.js;iisnode.yml

# Graceful shutdown
gracefulShutdownTimeout: 60000

# Enable debugging (set to false in production)
debuggingEnabled: false

# Enable XFF headers for proper client IP detection
enableXFF: true

# Promote server variables
promoteServerVars: LOGON_USER,AUTH_USER,AUTH_TYPE,REMOTE_ADDR,HTTP_X_FORWARDED_FOR

# Disable idle page out to keep the app running
idlePageOutTimePeriod: 0

# Enable recycling signal
recycleSignalEnabled: false
