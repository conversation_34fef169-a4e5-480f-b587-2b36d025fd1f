# Thunee Card Game

A multiplayer implementation of the Thunee card game with WebSocket support for real-time gameplay.

## Features

- Multiplayer lobby system with WebSockets
- Team-based gameplay (2 teams of 2 players)
- Real-time game state updates
- Responsive design for desktop and mobile
- Black and yellow themed UI

## Getting Started

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn

### Installation

1. Clone the repository

```bash
git clone https://github.com/yourusername/thunee-game.git
cd thunee-game
```

2. Install frontend dependencies

```bash
npm install
```

3. Install backend dependencies

```bash
cd server
npm install
cd ..
```

### Running the Application

1. Start the backend server

```bash
cd server
npm start
```

2. In a new terminal, start the frontend development server

```bash
npm run dev
```

3. Open your browser and navigate to `http://localhost:5173` (frontend will connect to `http://**************:3001` for the game server)

## How to Play

1. Enter your name on the main screen
2. Create a lobby or join an existing one with a lobby code
3. Wait for all 4 players to join (2 teams of 2 players)
4. The host can start the game once all players have joined
5. Follow the in-game instructions for dealing, selecting trump, and playing cards

## Game Rules

Thunee is a 4-player trick-taking card game with the following rules:

- Players are divided into 2 teams of 2 players each
- The deck consists of 24 cards (9, 10, J, Q, K, A from each suit)
- Card rankings: J (30 pts), 9 (20 pts), A (11 pts), 10 (10 pts), K (3 pts), Q (2 pts)
- Special calls include Thunee, Double, Jodhi, and Khanuck
- The dealer is determined by who gets a black jack
- The dealer shuffles, the player to the left cuts, and the dealer passes 4 cards to each player
- The player on the right selects the trump suit, then the remaining 2 cards are dealt
- Players must follow suit if possible
- Trump cards beat non-trump cards
- The winner of a trick leads the next one
- The team that reaches 105 points first wins the game
