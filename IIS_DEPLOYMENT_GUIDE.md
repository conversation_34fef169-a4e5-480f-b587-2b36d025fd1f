# IIS Deployment Guide for Thunee Game

## Problem Solved
The original error `EACCES: permission denied 0.0.0.0:3001` occurred because the Node.js server was trying to bind directly to port 3001, which conflicts with how iisnode works. When using iisnode, IIS handles the port binding and communicates with Node.js through named pipes.

## Changes Made

### 1. Server Code Changes (`server/index.js`)
- Added detection for iisnode environment using `process.env.IISNODE_VERSION`
- When running under iisnode, the server now uses named pipes instead of trying to bind to a specific port
- Maintains backward compatibility for standalone operation

### 2. Web Configuration (`web.config`)
- Replaced reverse proxy configuration with proper iisnode configuration
- Added URL rewriting rules to route API and WebSocket requests to Node.js
- Configured static file serving for frontend assets
- Added comprehensive iisnode settings for optimal performance

### 3. Additional Configuration Files
- `server/web.config`: Specific configuration for the server directory
- `iisnode.yml` and `server/iisnode.yml`: iisnode-specific settings
- `test-server.js`: Simple test script to verify configuration

## Deployment Steps

### Prerequisites
1. Ensure Node.js is installed on the server
2. Ensure iisnode is installed and configured in IIS
3. Ensure the IIS application pool has appropriate permissions

### Step 1: Deploy Files
1. Copy all files to `C:\inetpub\Thunee-FE\`
2. Ensure the directory structure is:
   ```
   C:\inetpub\Thunee-FE\
   ├── web.config
   ├── iisnode.yml
   ├── server/
   │   ├── index.js
   │   ├── web.config
   │   ├── iisnode.yml
   │   ├── package.json
   │   └── node_modules/
   └── [frontend files]
   ```

### Step 2: Install Dependencies
1. Navigate to the server directory: `cd C:\inetpub\Thunee-FE\server`
2. Install Node.js dependencies: `npm install`

### Step 3: Configure IIS
1. Create or update the IIS application pointing to `C:\inetpub\Thunee-FE\`
2. Ensure the application pool is set to "No Managed Code"
3. Verify that iisnode is properly installed and configured

### Step 4: Test the Configuration
1. Test locally first: `node test-server.js`
2. Access the IIS application in a browser
3. Check for any errors in the iisnode logs (located in `C:\inetpub\Thunee-FE\server\iisnode\`)

## Frontend Configuration

The frontend needs to connect to the correct URL when running under IIS. Update the socket service configuration:

### For Production (IIS)
The frontend should connect to the same domain/port as the website since iisnode handles the routing:
```javascript
// In src/services/socketService.ts
const SOCKET_SERVER_URL = window.location.origin; // Uses same domain/port as website
```

### For Development
Keep the existing configuration that allows port specification.

## Troubleshooting

### Common Issues

1. **Permission Errors**
   - Ensure the IIS application pool identity has read/execute permissions on the application directory
   - Check that Node.js is accessible from the application pool identity

2. **Module Not Found Errors**
   - Verify that `npm install` was run in the server directory
   - Check that all dependencies are properly installed

3. **WebSocket Connection Issues**
   - Ensure WebSocket support is enabled in IIS
   - Check that the URL rewriting rules are correctly routing socket.io requests

4. **iisnode Not Working**
   - Verify iisnode is installed: Check for `%programfiles%\iisnode\` directory
   - Ensure the iisnode module is registered in IIS
   - Check the iisnode logs for detailed error information

### Log Locations
- iisnode logs: `C:\inetpub\Thunee-FE\server\iisnode\`
- IIS logs: `C:\inetpub\logs\LogFiles\`
- Windows Event Logs: Check Application and System logs

### Testing Commands
```bash
# Test the server locally
node test-server.js

# Test the main server
cd server
node index.js

# Check if dependencies are installed
npm list
```

## Security Considerations

1. **Hide Sensitive Directories**
   - The web.config hides `node_modules` and `iisnode` directories from web access
   - Ensure no sensitive configuration files are accessible via web

2. **Application Pool Security**
   - Use a dedicated application pool with minimal permissions
   - Consider using a custom identity with only necessary permissions

3. **CORS Configuration**
   - Update CORS settings in production to restrict origins
   - Currently set to `*` for development

## Performance Optimization

1. **iisnode Settings**
   - Configured for optimal performance with connection pooling
   - Graceful shutdown handling
   - File watching for automatic restarts during development

2. **Static File Serving**
   - Static assets are served directly by IIS for better performance
   - Node.js only handles dynamic requests and WebSocket connections

## Next Steps

1. Test the deployment on your IIS server
2. Update frontend configuration for production
3. Monitor logs for any issues
4. Consider implementing health checks
5. Set up monitoring and alerting for production use
