/**
 * Handler for Khanak calls in the Thunee game
 */

import gameEndUtils from '../utils/server/gameEndUtils.js';

/**
 * Process a Khanak call
 *
 * @param {Object} lobby - The game lobby
 * @param {Object} matchedLobby - The matched lobby (if different from the main lobby)
 * @param {Object} player - The player who called <PERSON><PERSON>
 * @param {Object} io - The Socket.IO instance for emitting events
 * @returns {Object} The result of the Khanak call processing
 */
function processKhanakCall(lobby, matchedLobby, player, io) {
  console.log(`Processing Khanak call from player ${player.name} (Team ${player.team})`);

  // Get the player's team and the opposing team
  const playerTeam = player.team;
  const opposingTeam = playerTeam === 1 ? 2 : 1;

  // Get hands won by each team
  const team1HandIds = new Set();
  const team2HandIds = new Set();
  const team1Hands = [];
  const team2Hands = [];

  // Make sure hands array exists
  if (!lobby.hands) {
    lobby.hands = [];
  }

  // Process hands to avoid duplicates
  lobby.hands.forEach(hand => {
    if (hand.winningTeam === 1) {
      if (!team1HandIds.has(hand.id)) {
        team1HandIds.add(hand.id);
        team1Hands.push(hand);
      }
    } else if (hand.winningTeam === 2) {
      if (!team2HandIds.has(hand.id)) {
        team2HandIds.add(hand.id);
        team2Hands.push(hand);
      }
    }
  });

  const team1HandsWon = team1HandIds.size;
  const team2HandsWon = team2HandIds.size;

  // Get the hands won by the opposing team
  const opposingTeamHandsWon = opposingTeam === 1 ? team1HandsWon : team2HandsWon;
  const opposingTeamHands = opposingTeam === 1 ? team1Hands : team2Hands;

  // Check if the opposing team has won at least one hand
  if (opposingTeamHandsWon === 0) {
    console.log(`Opposing team (Team ${opposingTeam}) has not won any hands. Khanak call is invalid.`);

    // If opposing team has not won any hands, they automatically lose 4 balls
    return {
      isValid: false,
      reason: 'opposing_team_no_hands',
      ballsAwarded: 4,
      winningTeam: playerTeam,
      outcome: 'opposing_team_no_hands'
    };
  }

  // Check if the player's team has previously called Jodhi
  const jordhiCalls = lobby.jordhiCalls || [];
  const teamJordhiCalls = jordhiCalls.filter(call => call.playerTeam === playerTeam);

  if (teamJordhiCalls.length === 0) {
    console.log(`Team ${playerTeam} has not called any Jodhi. Khanak call is invalid.`);

    // If the team has not called any Jodhi, the Khanak call is invalid
    // The opposing team gets 4 balls
    return {
      isValid: false,
      reason: 'no_jordhi_calls',
      ballsAwarded: 4,
      winningTeam: opposingTeam,
      outcome: 'no_jordhi_calls'
    };
  }

  // Calculate the Khanak threshold
  // Sum all Jodhi points from the player's team (both valid and invalid calls)
  // Use a Set to track unique player+value+suit combinations to avoid double-counting
  let playerTeamJordhiPoints = 0;
  const processedPlayerCalls = new Set();

  teamJordhiCalls.forEach(call => {
    const callKey = `${call.playerId}-${call.value}-${call.jordhiSuit || ''}`;
    if (!processedPlayerCalls.has(callKey)) {
      processedPlayerCalls.add(callKey);
      playerTeamJordhiPoints += parseInt(call.value);
    }
  });

  // Sum all Jodhi points from the opposing team (both valid and invalid calls)
  const opposingTeamJordhiCalls = jordhiCalls.filter(call => call.playerTeam === opposingTeam);
  let opposingTeamJordhiPoints = 0;
  const processedOpposingCalls = new Set();

  opposingTeamJordhiCalls.forEach(call => {
    const callKey = `${call.playerId}-${call.value}-${call.jordhiSuit || ''}`;
    if (!processedOpposingCalls.has(callKey)) {
      processedOpposingCalls.add(callKey);
      opposingTeamJordhiPoints += parseInt(call.value);
    }
  });

  // Add 10 points for the last hand
  const khanakThreshold = playerTeamJordhiPoints + 10 - opposingTeamJordhiPoints;

  console.log(`Khanak threshold calculation:`);
  console.log(`- Team ${playerTeam} Jodhi points: ${playerTeamJordhiPoints}`);
  console.log(`- Add 10 for last hand: ${playerTeamJordhiPoints + 10}`);
  console.log(`- Subtract Team ${opposingTeam} Jodhi points: ${opposingTeamJordhiPoints}`);
  console.log(`- Final Khanak threshold: ${khanakThreshold}`);

  // Calculate the total points from the opposing team's hands
  const opposingTeamPoints = opposingTeamHands.reduce((sum, hand) => sum + (hand.points || 0), 0);
  console.log(`Opposing team (Team ${opposingTeam}) has ${opposingTeamPoints} points from ${opposingTeamHandsWon} hands`);

  // Store the Khanak call in the lobby
  if (!lobby.khanakCalls) {
    lobby.khanakCalls = [];
    if (matchedLobby) matchedLobby.khanakCalls = [];
  }

  const khanakCall = {
    playerId: player.id,
    playerName: player.name,
    playerTeam: playerTeam,
    opposingTeam: opposingTeam,
    threshold: khanakThreshold,
    opposingTeamPoints: opposingTeamPoints,
    opposingTeamHands: opposingTeamHands,
    teamJordhiPoints: playerTeamJordhiPoints,
    opposingTeamJordhiPoints: opposingTeamJordhiPoints
  };

  lobby.khanakCalls.push(khanakCall);
  if (matchedLobby) matchedLobby.khanakCalls.push(khanakCall);

  // Store the current Khanak call
  lobby.currentKhanakCall = khanakCall;
  if (matchedLobby) matchedLobby.currentKhanakCall = khanakCall;

  return {
    isValid: true,
    khanakCall
  };
}

/**
 * Process the result of a Khanak call after the last hand is played
 *
 * @param {Object} lobby - The game lobby
 * @param {Object} matchedLobby - The matched lobby (if different from the main lobby)
 * @param {Object} handWinner - The player who won the last hand
 * @returns {Object} The result of the Khanak call
 */
function processKhanakResult(lobby, matchedLobby, handWinner) {
  // Check if there's an active Khanak call
  if (!lobby.currentKhanakCall) {
    return null;
  }

  const khanakCall = lobby.currentKhanakCall;
  const playerTeam = khanakCall.playerTeam;
  const opposingTeam = khanakCall.opposingTeam;
  const khanakThreshold = khanakCall.threshold;
  const opposingTeamPoints = khanakCall.opposingTeamPoints;

  // Check if the Khanak caller's team won the last hand
  const lastHandWinnerTeam = handWinner.team;

  if (lastHandWinnerTeam !== playerTeam) {
    console.log(`Khanak caller's team (Team ${playerTeam}) did not win the last hand. Khanak call is unsuccessful.`);

    // If the Khanak caller's team did not win the last hand, they lose 4 balls
    return {
      success: false,
      reason: 'last_hand_lost',
      ballsAwarded: 4,
      winningTeam: opposingTeam,
      outcome: 'last_hand_lost',
      khanakCall
    };
  }

  // Check for special ball limit rules before proceeding with normal Khanak rules
  const specialRulesCheck = gameEndUtils.checkSpecialBallLimitRules(lobby, playerTeam, 'khanak');

  if (specialRulesCheck.applySpecialRules) {
    // Special rules apply - opposing team gets 4 balls, caller gets none
    console.log(`Khanak special rule applied! Team ${playerTeam} called Khanak but special ball limit rules apply. ${specialRulesCheck.reason}. Team ${specialRulesCheck.winningTeam} gets ${specialRulesCheck.ballsAwarded} balls.`);

    return {
      success: false,
      reason: 'special_ball_limit_rule',
      ballsAwarded: specialRulesCheck.ballsAwarded,
      winningTeam: specialRulesCheck.winningTeam,
      outcome: specialRulesCheck.outcome,
      specialRuleApplied: true,
      specialRuleReason: specialRulesCheck.reason,
      khanakCall
    };
  }

  // Check if the opposing team's points are less than the Khanak threshold
  if (opposingTeamPoints < khanakThreshold) {
    console.log(`Opposing team (Team ${opposingTeam}) has ${opposingTeamPoints} points, which is less than the Khanak threshold of ${khanakThreshold}. Khanak call is successful.`);

    // If the opposing team's points are less than the threshold, the Khanak call is successful
    // The Khanak caller's team gets 3 balls
    return {
      success: true,
      reason: 'points_below_threshold',
      ballsAwarded: 3,
      winningTeam: playerTeam,
      outcome: 'points_below_threshold',
      khanakCall
    };
  } else {
    console.log(`Opposing team (Team ${opposingTeam}) has ${opposingTeamPoints} points, which is equal to or greater than the Khanak threshold of ${khanakThreshold}. Khanak call is unsuccessful.`);

    // If the opposing team's points are equal to or greater than the threshold, the Khanak call is unsuccessful
    // The opposing team gets 4 balls
    return {
      success: false,
      reason: 'points_above_threshold',
      ballsAwarded: 4,
      winningTeam: opposingTeam,
      outcome: 'points_above_threshold',
      khanakCall
    };
  }
}

export default {
  processKhanakCall,
  processKhanakResult
};
