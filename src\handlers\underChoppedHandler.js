/**
 * Hand<PERSON> for validating "Under chopped" 4-ball claims
 */

/**
 * Validate an "Under chopped" 4-ball claim
 * @param {Object} lobby - The game lobby
 * @param {String} accuserId - The ID of the player making the accusation
 * @param {String} accusedId - The ID of the player being accused
 * @param {Number} handNumber - The hand number where the alleged violation occurred
 * @returns {Object} The result of the validation
 */
function validateUnderChoppedClaim(lobby, accuserId, accusedId, handNumber) {
  // Custom trump ranking for Thunee variant (highest to lowest)
  // Queen > King > 10 > Ace > 9 > Jack
  const customTrumpRanking = ['J', '9', 'A', '10', 'K', 'Q'];

  // Find the players involved
  const accuser = lobby.players.find(p => p.id === accuserId);
  const accused = lobby.players.find(p => p.id === accusedId);

  if (!accuser || !accused) {
    console.error(`Player not found: accuser=${accuserId}, accused=${accusedId}`);
    return {
      isValid: false,
      reason: 'Player not found',
      accuserTeam: accuser?.team || 1,
      accusedTeam: accused?.team || 2,
      winningTeam: accused?.team || 2, // If accused not found, default to their team winning
      ballsAwarded: 4,
      customTrumpRanking
    };
  }

  const accuserTeam = accuser.team;
  const accusedTeam = accused.team;

  // Make sure we have hands data
  if (!lobby.hands || lobby.hands.length === 0) {
    console.error('No hands data available');
    return {
      isValid: false,
      reason: 'No hands data available',
      accuserTeam,
      accusedTeam,
      winningTeam: accusedTeam, // Accused team wins if no data available
      ballsAwarded: 4,
      customTrumpRanking
    };
  }

  // Find the specific hand in question
  const hand = lobby.hands.find(h => h.id === handNumber);
  if (!hand) {
    console.error(`Hand #${handNumber} not found`);
    return {
      isValid: false,
      reason: `Hand #${handNumber} not found`,
      accuserTeam,
      accusedTeam,
      winningTeam: accusedTeam, // Accused team wins if hand not found
      ballsAwarded: 4,
      customTrumpRanking
    };
  }

  // Check if we have the play data for the accused player
  const accusedPlay = hand.plays?.find(p => p.playerId === accusedId);
  if (!accusedPlay) {
    console.error(`No play data found for player ${accusedId} in hand #${handNumber}`);
    return {
      isValid: false,
      reason: 'No play data found for accused player',
      accuserTeam,
      accusedTeam,
      winningTeam: accusedTeam, // Accused team wins if no play data
      ballsAwarded: 4,
      customTrumpRanking
    };
  }

  // Get the trump suit for this game
  const trumpSuit = lobby.trumpSuit;
  if (!trumpSuit) {
    console.error('No trump suit found for the game');
    return {
      isValid: false,
      reason: 'No trump suit information available',
      accuserTeam,
      accusedTeam,
      winningTeam: accusedTeam, // Accused team wins if no trump suit info
      ballsAwarded: 4,
      customTrumpRanking
    };
  }

  // Get the card played by the accused
  const cardPlayed = accusedPlay.card;
  if (!cardPlayed || !cardPlayed.suit || !cardPlayed.value) {
    console.error(`No card data found for player ${accusedId} in hand #${handNumber}`);
    return {
      isValid: false,
      reason: 'No card data found for accused player',
      accuserTeam,
      accusedTeam,
      winningTeam: accusedTeam, // Accused team wins if no card data
      ballsAwarded: 4,
      customTrumpRanking
    };
  }

  // Get the hand the accused had before playing
  let handBeforePlay = accusedPlay.handBeforePlay || [];

  // If handBeforePlay is empty, try to get it from the lobby's playerCards
  if (handBeforePlay.length === 0 && lobby.playerCards && lobby.playerCards[accusedId]) {
    // Make a deep copy of the player's cards at the time of the 4-ball call
    handBeforePlay = JSON.parse(JSON.stringify(lobby.playerCards[accusedId] || []));

    // Add the card that was played (since it's already removed from the player's hand)
    if (cardPlayed) {
      handBeforePlay.push({
        suit: cardPlayed.suit,
        value: cardPlayed.value
      });
    }

    console.log(`Retrieved ${handBeforePlay.length} cards from player ${accusedId}'s current hand for under-chopped validation`);
  }

  // Check if the played card is a trump card
  const isPlayedCardTrump = cardPlayed.suit === trumpSuit;

  // If the played card is not a trump, then it's not under chopping
  if (!isPlayedCardTrump) {
    console.log(`Card played (${cardPlayed.value} of ${cardPlayed.suit}) is not a trump card (${trumpSuit})`);
    return {
      isValid: false,
      reason: 'Card played is not a trump card',
      invalidReason: 'No Trump Played',
      accuserTeam,
      accusedTeam,
      winningTeam: accusedTeam, // Accused team wins
      ballsAwarded: 4,
      trumpSuit,
      cardPlayed,
      handBeforePlaying: handBeforePlay,
      customTrumpRanking
    };
  }

  // Get all trump cards in the player's hand before playing
  const trumpCardsInHand = handBeforePlay.filter(card => card.suit === trumpSuit);

  // If there are no other trump cards, then it's not under chopping
  if (trumpCardsInHand.length === 0) {
    console.log(`Player had no trump cards in hand`);
    return {
      isValid: false,
      reason: 'Player had no trump cards in hand',
      accuserTeam,
      accusedTeam,
      winningTeam: accusedTeam, // Accused team wins
      ballsAwarded: 4,
      trumpSuit,
      cardPlayed,
      handBeforePlaying: handBeforePlay,
      customTrumpRanking
    };
  }

  // NEW RULE: Check if trump was the lead suit in this hand
  // Get the first card played in this hand
  const firstPlay = hand.plays && hand.plays.length > 0 ? hand.plays[0] : null;
  const leadSuit = firstPlay?.card?.suit;

  if (leadSuit === trumpSuit) {
    console.log(`Trump (${trumpSuit}) was the lead suit in hand #${handNumber}`);

    // Collect all cards played in this hand
    const handCards = hand.plays.map(play => {
      const player = lobby.players.find(p => p.id === play.playerId);
      return {
        suit: play.card.suit,
        value: play.card.value,
        playedBy: play.playerId,
        playerName: player ? player.name : 'Unknown',
        playerTeam: player ? player.team : 0,
        playOrder: play.playOrder || 0
      };
    });

    console.log(`Collected ${handCards.length} cards for hand #${handNumber}`);

    return {
      isValid: false,
      reason: 'Trump was the lead suit in this hand',
      invalidReason: 'Trump was the Lead Suit',
      accuserTeam,
      accusedTeam,
      winningTeam: accusedTeam, // Accused team wins
      ballsAwarded: 4,
      trumpSuit,
      cardPlayed,
      handBeforePlaying: handBeforePlay,
      customTrumpRanking,
      leadSuit,
      selectedHandCards: handCards
    };
  }

  // Get the rank of the played card
  const playedCardRank = customTrumpRanking.indexOf(cardPlayed.value);

  // Check if the player had a higher trump card
  const hasHigherTrump = trumpCardsInHand.some(card => {
    const cardRank = customTrumpRanking.indexOf(card.value);
    return cardRank < playedCardRank; // Lower index means higher rank
  });

  // NEW RULE: Check if the played trump was the highest trump in the hand
  // Get all trump cards played in this hand
  const trumpCardsPlayed = hand.plays
    .filter(play => play.card && play.card.suit === trumpSuit)
    .map(play => play.card);

  console.log(`Trump cards played in hand #${handNumber}:`,
    trumpCardsPlayed.map(card => `${card.value} of ${card.suit}`).join(', '));

  // Find the highest trump card played in this hand
  let highestTrumpPlayed = null;
  let highestTrumpRank = -1;

  trumpCardsPlayed.forEach(card => {
    const rank = customTrumpRanking.indexOf(card.value);
    console.log(`Card ${card.value} of ${card.suit} has rank ${rank} (lower index = higher rank)`);
    if (rank > -1 && (highestTrumpPlayed === null || rank < highestTrumpRank)) {
      highestTrumpPlayed = card;
      highestTrumpRank = rank;
      console.log(`New highest trump: ${card.value} of ${card.suit} with rank ${rank}`);
    }
  });

  // In Thunee, the custom trump ranking is: Queen > King > 10 > Ace > 9 > Jack
  // Lower index in customTrumpRanking means higher rank
  console.log(`Highest trump played: ${highestTrumpPlayed ? highestTrumpPlayed.value + ' of ' + highestTrumpPlayed.suit : 'None'}`);
  console.log(`Accused player's card: ${cardPlayed.value} of ${cardPlayed.suit}`);
  console.log(`Custom trump ranking: ${customTrumpRanking.join(' > ')}`);

  // Check if the accused player's card is the highest trump played
  const isHighestTrumpPlayed = highestTrumpPlayed &&
    highestTrumpPlayed.suit === cardPlayed.suit &&
    highestTrumpPlayed.value === cardPlayed.value;

  if (isHighestTrumpPlayed) {
    console.log(`Player played the highest trump card (${cardPlayed.value} of ${cardPlayed.suit}) in the hand`);
    return {
      isValid: false,
      reason: 'Player played the highest trump card in the hand',
      invalidReason: 'Highest Trump Played',
      accuserTeam,
      accusedTeam,
      winningTeam: accusedTeam, // Accused team wins
      ballsAwarded: 4,
      trumpSuit,
      cardPlayed,
      handBeforePlaying: handBeforePlay,
      customTrumpRanking,
      highestTrumpPlayed,
      selectedHandCards: hand.plays.map(play => {
        const player = lobby.players.find(p => p.id === play.playerId);
        return {
          suit: play.card.suit,
          value: play.card.value,
          playedBy: play.playerId,
          playerName: player ? player.name : 'Unknown',
          playerTeam: player ? player.team : 0,
          playOrder: play.playOrder || 0
        };
      })
    };
  }

  // NEW RULE: Check if player had only trump cards left
  const nonTrumpCards = handBeforePlay.filter(card => card.suit !== trumpSuit);
  const hadOnlyTrumps = nonTrumpCards.length === 0;

  if (hadOnlyTrumps) {
    console.log(`Player had only trump cards in hand`);

    // Collect all cards played in this hand
    const handCards = hand.plays.map(play => {
      const player = lobby.players.find(p => p.id === play.playerId);
      return {
        suit: play.card.suit,
        value: play.card.value,
        playedBy: play.playerId,
        playerName: player ? player.name : 'Unknown',
        playerTeam: player ? player.team : 0,
        playOrder: play.playOrder || 0
      };
    });

    console.log(`Collected ${handCards.length} cards for hand #${handNumber}`);

    return {
      isValid: false,
      reason: 'Player had only trump cards in hand',
      invalidReason: 'Player Had Only Trumps',
      accuserTeam,
      accusedTeam,
      winningTeam: accusedTeam, // Accused team wins
      ballsAwarded: 4,
      trumpSuit,
      cardPlayed,
      handBeforePlaying: handBeforePlay,
      customTrumpRanking,
      selectedHandCards: handCards,
      leadSuit
    };
  }

  // NEW RULE: Check if player had cards of the lead suit
  const leadSuitCards = handBeforePlay.filter(card => card.suit === leadSuit);
  const hadLeadSuitCards = leadSuitCards.length > 0;

  // The accusation is valid if:
  // 1. The player had a higher trump card but played a lower one
  // 2. The player had non-trump cards they could have played
  const isValid = hasHigherTrump && !hadOnlyTrumps;

  // Determine the winning team based on the validation result
  const winningTeam = isValid ? accuserTeam : accusedTeam;

  // Collect all cards played in this hand by all players
  const selectedHandCards = [];

  // If hand.plays exists, use it
  if (hand.plays && Array.isArray(hand.plays)) {
    console.log(`Found ${hand.plays.length} plays in hand #${handNumber}`);

    // Sort plays by the order they were played (if available)
    const sortedPlays = [...hand.plays].sort((a, b) => {
      // If playOrder is available, use it
      if (a.playOrder !== undefined && b.playOrder !== undefined) {
        return a.playOrder - b.playOrder;
      }
      // Otherwise, keep original order
      return 0;
    });

    sortedPlays.forEach((play, index) => {
      // Find the player who played this card
      const player = lobby.players.find(p => p.id === play.playerId);
      if (player && play.card) {
        console.log(`Adding card to selectedHandCards: ${play.card.value} of ${play.card.suit} played by ${player.name}`);
        selectedHandCards.push({
          suit: play.card.suit,
          value: play.card.value,
          playedBy: play.playerId,
          playerName: player.name,
          playerTeam: player.team,
          playOrder: index // Add play order for proper display
        });
      }
    });
  }
  // If hand.plays doesn't exist, try to use hand.cards
  else if (hand.cards && Array.isArray(hand.cards)) {
    console.log(`Found ${hand.cards.length} cards in hand #${handNumber}`);

    hand.cards.forEach((card, index) => {
      if (card.playedBy) {
        // Find the player who played this card
        const player = lobby.players.find(p => p.id === card.playedBy);
        if (player) {
          console.log(`Adding card to selectedHandCards: ${card.value} of ${card.suit} played by ${player.name}`);
          selectedHandCards.push({
            suit: card.suit,
            value: card.value,
            playedBy: card.playedBy,
            playerName: player.name,
            playerTeam: player.team,
            playOrder: index // Add play order for proper display
          });
        }
      }
    });
  }

  console.log(`Selected hand cards for hand #${handNumber}: ${selectedHandCards.length} cards collected`);

  // If no cards were found, try to reconstruct from the hand history
  if (selectedHandCards.length === 0 && lobby.handHistory && Array.isArray(lobby.handHistory)) {
    console.log(`Trying to reconstruct hand #${handNumber} from hand history`);

    const handHistoryEntry = lobby.handHistory.find(h => h.handId === handNumber);
    if (handHistoryEntry && handHistoryEntry.cards && Array.isArray(handHistoryEntry.cards)) {
      handHistoryEntry.cards.forEach((card, index) => {
        if (card.playedBy) {
          // Find the player who played this card
          const player = lobby.players.find(p => p.id === card.playedBy);
          if (player) {
            console.log(`Adding card from history to selectedHandCards: ${card.value} of ${card.suit} played by ${player.name}`);
            selectedHandCards.push({
              suit: card.suit,
              value: card.value,
              playedBy: card.playedBy,
              playerName: player.name,
              playerTeam: player.team,
              playOrder: index
            });
          }
        }
      });
    }
  }

  // If we still don't have any cards, create a dummy entry for the accused player's card
  // This ensures we at least show the card that was played
  if (selectedHandCards.length === 0 && cardPlayed) {
    console.log(`Creating dummy entry for accused player's card: ${cardPlayed.value} of ${cardPlayed.suit}`);
    selectedHandCards.push({
      suit: cardPlayed.suit,
      value: cardPlayed.value,
      playedBy: accusedId,
      playerName: accused.name,
      playerTeam: accused.team,
      playOrder: 0
    });
  }

  // Log the final selected hand cards for debugging
  console.log(`Final selected hand cards for hand #${handNumber}:`,
    selectedHandCards.map(card => `${card.value} of ${card.suit} played by ${card.playerName}`).join(', '));

  // Log the player's hand before playing for debugging
  console.log(`Player's hand before playing:`,
    handBeforePlay.map(card => `${card.value} of ${card.suit}`).join(', '));

  return {
    isValid,
    isPlayedCardTrump,  // Add this property to indicate if the played card is a trump
    hasHigherTrump,     // Add this property to indicate if the player had a higher trump
    trumpCardsCount: trumpCardsInHand.length, // Add the count of trump cards in hand
    hadOnlyTrumps,      // Add this property to indicate if player had only trump cards
    hadLeadSuitCards,   // Add this property to indicate if player had cards of the lead suit
    leadSuit,           // Add the lead suit for this hand
    isHighestTrumpPlayed, // Add this property to indicate if player played the highest trump
    reason: isValid
      ? `Player had a higher trump card but played ${cardPlayed.value} of ${cardPlayed.suit} when they had other non-trump cards`
      : leadSuit === trumpSuit
        ? `Trump was the lead suit in this hand`
        : isHighestTrumpPlayed
          ? `Player played the highest trump card in the hand`
          : !isPlayedCardTrump
            ? `Card played (${cardPlayed.value} of ${cardPlayed.suit}) is not a trump card (${trumpSuit})`
            : hadOnlyTrumps
              ? `Player had only trump cards in hand`
              : trumpCardsInHand.length === 0
                ? `Player had no trump cards in hand`
                : `Player did not have a higher trump card than the one played`,
    invalidReason: !isValid ? (
      leadSuit === trumpSuit
        ? 'Trump was the Lead Suit'
        : isHighestTrumpPlayed
          ? 'Highest Trump Played'
          : !isPlayedCardTrump
            ? 'No Trump Played'
            : hadOnlyTrumps
              ? 'Player Had Only Trumps'
              : 'No Higher Trump'
    ) : null,
    accuserTeam,
    accusedTeam,
    winningTeam,
    ballsAwarded: 4,
    trumpSuit,
    cardPlayed,
    handBeforePlaying: handBeforePlay,
    customTrumpRanking,
    selectedHandCards // Add the selected hand cards to the result
  };
}

export default {
  validateUnderChoppedClaim
};
