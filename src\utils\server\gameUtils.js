/**
 * Utility functions for Thunee game logic
 */

/**
 * Determine the winner of a hand based on the played cards, lead suit, and trump suit
 * @param {Array} playedCards - Array of cards played in the hand
 * @param {String} trumpSuit - The trump suit for the game
 * @param {String} thuneePlayerId - The ID of the player who called <PERSON><PERSON><PERSON> (if any)
 * @returns {Object} The winning card and player ID
 */
function determineHandWinner(playedCards, trumpSuit, thuneePlayerId = null) {
  if (!playedCards || playedCards.length === 0) {
    return null;
  }

  // Remove duplicate cards (keep only one card per player)
  const uniquePlayerIds = new Set();
  const uniqueCards = [];

  for (const card of playedCards) {
    if (!card.playedBy) {
      console.error('Card missing playedBy property:', card);
      continue; // Skip cards without playedBy
    }

    if (!uniquePlayerIds.has(card.playedBy)) {
      uniquePlayerIds.add(card.playedBy);
      uniqueCards.push(card);
    }
  }

  console.log(`Determining hand winner with ${uniqueCards.length} unique cards from ${uniquePlayerIds.size} players`);

  // Make sure we have at least one card
  if (uniqueCards.length === 0) {
    console.error('No valid cards found after removing duplicates');
    return null;
  }

  // Get the lead suit (the suit of the first card played)
  const leadSuit = uniqueCards[0].suit;

  // If this is the first hand after a Thunee call, the first card played becomes trump
  if (thuneePlayerId && uniqueCards[0].playedBy === thuneePlayerId && uniqueCards.length === 4) {
    // This is the first hand and the Thunee caller played first
    // Update the trump suit to match the first card played
    trumpSuit = leadSuit;
    console.log(`Thunee called: First card played (${uniqueCards[0].value} of ${leadSuit}) becomes trump`);
  }

  // Card rankings for trump suit (highest to lowest)
  const trumpRanking = { 'J': 6, '9': 5, 'A': 4, '10': 3, 'K': 2, 'Q': 1 };

  // Card rankings for non-trump suits (highest to lowest)
  // In Thunee, the ranking is the same for all suits: J > 9 > A > 10 > K > Q
  const nonTrumpRanking = { 'J': 6, '9': 5, 'A': 4, '10': 3, 'K': 2, 'Q': 1 };

  // Find the highest card
  let winningCard = uniqueCards[0];
  let winningPlayerId = uniqueCards[0].playedBy;
  let winReason = 'First card played';

  // Check each card against the current winner
  for (let i = 1; i < uniqueCards.length; i++) {
    const currentCard = uniqueCards[i];

    // Case 1: Current card is trump and winning card is not trump
    if (currentCard.suit === trumpSuit && winningCard.suit !== trumpSuit) {
      winningCard = currentCard;
      winningPlayerId = currentCard.playedBy;
      winReason = 'Trump beats non-trump';
    }
    // Case 2: Both cards are trump
    else if (currentCard.suit === trumpSuit && winningCard.suit === trumpSuit) {
      if (trumpRanking[currentCard.value] > trumpRanking[winningCard.value]) {
        winningCard = currentCard;
        winningPlayerId = currentCard.playedBy;
        winReason = 'Higher trump card';
      }
    }
    // Case 3: Current card matches lead suit and winning card is not trump
    else if (currentCard.suit === leadSuit && winningCard.suit === leadSuit) {
      if (nonTrumpRanking[currentCard.value] > nonTrumpRanking[winningCard.value]) {
        winningCard = currentCard;
        winningPlayerId = currentCard.playedBy;
        winReason = 'Higher card of lead suit';
      }
    }
    // Case 4: Current card matches lead suit but winning card is trump
    else if (currentCard.suit === leadSuit && winningCard.suit === trumpSuit) {
      // Trump always wins, so do nothing
    }
    // Case 5: Current card doesn't match lead suit and is not trump
    else {
      // This card cannot win, so do nothing
    }
  }

  // Calculate points for the hand
  let points = 0;
  uniqueCards.forEach(card => {
    points += card.points || 0;
  });

  // Check if this is a Thunee hand and the Thunee caller didn't win
  let isThuneeFailure = false;
  let thuneePartnerWon = false;

  if (thuneePlayerId && winningPlayerId !== thuneePlayerId) {
    isThuneeFailure = true;

    // Check if the winner is the Thunee caller's partner
    // We'll need to determine this in the calling code
    console.log(`Thunee failure: ${thuneePlayerId} called Thunee but ${winningPlayerId} won the hand`);
  }

  return {
    winningCard,
    winningPlayerId,
    points,
    winReason,
    isThuneeFailure,
    thuneePartnerWon,
    trumpSuit // Return the possibly updated trump suit
  };
}

/**
 * Calculate the total points for a hand
 * @param {Array} cards - Array of cards in the hand
 * @returns {Number} Total points
 */
function calculateHandPoints(cards) {
  return cards.reduce((total, card) => total + (card.points || 0), 0);
}

export default {
  determineHandWinner,
  calculateHandPoints
};
