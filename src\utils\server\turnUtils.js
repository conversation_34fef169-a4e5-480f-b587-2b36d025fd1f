/**
 * Utility functions for handling player turns and turn timers
 */

import timeframeUtils from './timeframeUtils.js';

/**
 * Set the next player's turn with a timer
 * @param {Object} io - Socket.io instance
 * @param {Object} lobby - The lobby object
 * @param {Object} matchedLobby - The matched lobby object
 * @param {String} playerId - The ID of the player whose turn it is
 * @param {Boolean} isHandComplete - Whether this is the end of a hand
 * @param {Object} resetUtils - The reset utilities (optional)
 * @param {Object} gameEndUtils - The game end utilities (optional)
 */
function setPlayerTurn(io, lobby, matchedLobby, playerId, isHandComplete = false, resetUtils = null, gameEndUtils = null) {
  // Get the lobby code
  const lobbyCode = lobby.lobbyCode;
  const matchedLobbyCode = matchedLobby.lobbyCode;

  // Clear any existing turn timer
  if (lobby.turnTimer) {
    clearTimeout(lobby.turnTimer);
    lobby.turnTimer = null;
  }

  // Check if this is the first player's turn after Thunee opportunities
  const isFirstPlayerAfterThunee = lobby.thuneeOpportunitiesComplete && lobby.firstPlayerId === playerId;

  // Send player_turn event to both lobbies
  io.to(lobbyCode).emit('player_turn', {
    playerId,
    isFirstPlayerAfterThunee
  });

  if (matchedLobbyCode !== lobbyCode) {
    io.to(matchedLobbyCode).emit('player_turn', {
      playerId,
      isFirstPlayerAfterThunee
    });
  }

  // If this is the end of a hand, don't start a timer
  if (isHandComplete) {
    return;
  }

  // Initialize the turn timer
  const turnTimerState = timeframeUtils.initTurnTimer(lobby, playerId);
  const timeframe = turnTimerState.timeRemaining;

  console.log(`Starting turn timer for player ${playerId} with ${timeframe} seconds`);

  // Set up a timer to update clients about the remaining time
  const updateInterval = setInterval(() => {
    // Update the turn timer
    const updatedTimer = timeframeUtils.updateTurnTimer(lobby);

    if (!updatedTimer || !updatedTimer.timerActive) {
      clearInterval(updateInterval);
      lobby.updateInterval = null;
      return;
    }

    // Send turn_timer_update event to both lobbies
    io.to(lobbyCode).emit('turn_timer_update', {
      playerId,
      timeRemaining: updatedTimer.currentRemaining
    });

    if (matchedLobbyCode !== lobbyCode) {
      io.to(matchedLobbyCode).emit('turn_timer_update', {
        playerId,
        timeRemaining: updatedTimer.currentRemaining
      });
    }
  }, 100); // Update every 100ms for smoother countdown

  // Store the update interval reference so we can clean it up later
  lobby.updateInterval = updateInterval;

  // Set up a timer to handle timeout
  lobby.turnTimer = setTimeout(() => {
    // Stop the update interval
    clearInterval(updateInterval);
    lobby.updateInterval = null;

    // Check if the ball has already been completed (to prevent timeout after ball ends)
    if (lobby.currentHandId >= 6) {
      console.log(`Ball already completed, ignoring timeout for player ${playerId}`);
      return;
    }

    // Check if a 4-ball result is currently being processed
    if (lobby.fourBallInProgress || lobby.ballCompleted) {
      console.log(`4-ball or ball completion in progress, ignoring timeout for player ${playerId}`);
      return;
    }

    // Additional check: if ball scores indicate a completed ball (4 balls to any team)
    if (lobby.ballScores && (lobby.ballScores.team1 >= 4 || lobby.ballScores.team2 >= 4)) {
      console.log(`Ball already completed with scores Team 1: ${lobby.ballScores.team1}, Team 2: ${lobby.ballScores.team2}, ignoring timeout for player ${playerId}`);
      return;
    }

    // Check if the player has already played a card
    if (!lobby.currentHandCards || !lobby.currentHandCards.some(card => card.playedBy === playerId)) {
      console.log(`Player ${playerId} timed out after ${timeframe} seconds`);

      // Find the player who timed out
      const timedOutPlayer = lobby.players.find(p => p.id === playerId);
      if (!timedOutPlayer) {
        console.error(`Timed out player ${playerId} not found in lobby ${lobbyCode}`);
        return;
      }

      // Determine the opposing team
      const opposingTeam = timedOutPlayer.team === 1 ? 2 : 1;

      // Award a ball to the opposing team
      if (!lobby.ballScores) {
        lobby.ballScores = { team1: 0, team2: 0 };
        matchedLobby.ballScores = { team1: 0, team2: 0 };
      }

      // For any player who doesn't play within the time limit, award a ball to the opposing team
      // We'll keep track if this is the first player after Thunee opportunities for informational purposes
      const isFirstPlayerAfterThunee = lobby.thuneeOpportunitiesComplete && lobby.firstPlayerId === playerId;
      const ballsToAward = 1; // Award 1 ball regardless of which player times out

      lobby.ballScores[`team${opposingTeam}`] += ballsToAward;
      matchedLobby.ballScores[`team${opposingTeam}`] += ballsToAward;

      // Notify all players of the timeout
      const timeoutData = {
        playerId,
        playerName: timedOutPlayer.name,
        playerTeam: timedOutPlayer.team,
        opposingTeam,
        ballsAwarded: ballsToAward,
        isFirstPlayerAfterThunee,
        message: `${timedOutPlayer.name} didn't play within the time limit. Team ${opposingTeam} wins a ball.`
      };

      io.to(lobbyCode).emit('player_timeout', timeoutData);

      if (matchedLobbyCode !== lobbyCode) {
        io.to(matchedLobbyCode).emit('player_timeout', timeoutData);
      }

      // Reset for next ball
      if (resetUtils) {
        resetUtils.resetBallState(lobby, matchedLobby);
      }

      console.log('Ball state reset complete after timeout - all timers have been stopped');

      // Reset hand counter and points
      lobby.currentHandId = 0;
      matchedLobby.currentHandId = 0;
      lobby.ballPoints = { team1: 0, team2: 0 };
      matchedLobby.ballPoints = { team1: 0, team2: 0 };

      // Get the current ball ID
      const ballId = (lobby.currentBallId || 0) + 1;
      lobby.currentBallId = ballId;
      matchedLobby.currentBallId = ballId;

      // Send ball_completed event to both lobbies
      const ballCompletedData = {
        ballId,
        winner: opposingTeam,
        points: {
          team1: 0,
          team2: 0
        },
        nextDealer: lobby.dealerId, // Keep the same dealer
        ballScores: lobby.ballScores,
        timeout: true,
        timedOutPlayer: {
          id: playerId,
          name: timedOutPlayer.name,
          team: timedOutPlayer.team
        },
        ballsAwarded: ballsToAward,
        isFirstPlayerAfterThunee,
        timeoutMessage: `${timedOutPlayer.name} didn't play within the time limit. Team ${opposingTeam} wins a ball.`
      };

      io.to(lobbyCode).emit('ball_completed', ballCompletedData);
      io.to(matchedLobbyCode).emit('ball_completed', ballCompletedData);

      // Add ball to game history and check for game end
      if (gameEndUtils) {
        gameEndUtils.addBallToHistory(lobby, ballCompletedData);
        if (matchedLobby !== lobby) {
          gameEndUtils.addBallToHistory(matchedLobby, ballCompletedData);
        }

        // Check if game should end
        const gameEndCheck = gameEndUtils.checkGameEnd(lobby);
        if (gameEndCheck.gameEnded) {
          console.log(`Game ended! Team ${gameEndCheck.winner} wins!`);

          const gameHistory = gameEndUtils.getGameHistory(lobby);
          const gameEndData = {
            ...gameEndCheck,
            gameHistory
          };

          // Emit game_ended event to both lobbies
          io.to(lobbyCode).emit('game_ended', gameEndData);
          io.to(matchedLobbyCode).emit('game_ended', gameEndData);

          return; // Don't continue to next ball
        }
      }

      // Reset game phase to shuffle for the next ball
      setTimeout(() => {
        // Send a clear message to all players about the timeout rule
        io.to(lobbyCode).emit('game_message', {
          message: `${timedOutPlayer.name} didn't play within the time limit. The opposite team (Team ${opposingTeam}) wins a ball.`,
          type: 'timeout'
        });
        io.to(matchedLobbyCode).emit('game_message', {
          message: `${timedOutPlayer.name} didn't play within the time limit. The opposite team (Team ${opposingTeam}) wins a ball.`,
          type: 'timeout'
        });

        // Update game phase after showing the message
        setTimeout(() => {
          io.to(lobbyCode).emit('game_phase_updated', { phase: 'shuffle' });
          io.to(matchedLobbyCode).emit('game_phase_updated', { phase: 'shuffle' });
        }, 3000); // 3 second delay after showing the message
      }, 2000); // 2 second delay before showing the message
    }
  }, timeframe * 1000);
}

/**
 * Stop the turn timer
 * @param {Object} lobby - The lobby object
 */
function stopTurnTimer(lobby) {
  console.log(`Stopping turn timer for lobby ${lobby.lobbyCode}`);

  if (lobby.turnTimer) {
    clearTimeout(lobby.turnTimer);
    lobby.turnTimer = null;
  }

  // Also clear any update intervals that might be running
  if (lobby.updateInterval) {
    clearInterval(lobby.updateInterval);
    lobby.updateInterval = null;
  }

  timeframeUtils.stopTurnTimer(lobby);
}

export default {
  setPlayerTurn,
  stopTurnTimer
};
