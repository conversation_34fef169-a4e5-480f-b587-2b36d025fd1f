import express from 'express';
import http from 'http';
import { Server } from 'socket.io';
import cors from 'cors';

const app = express();
app.use(cors());

const server = http.createServer(app);
const io = new Server(server, {
  cors: {
    origin: '*', // In production, restrict this to your frontend URL
    methods: ['GET', 'POST']
  },
  transports: ['websocket', 'polling'],
});

// Store active rooms and their participants
const rooms = new Map();

// Helper function to get all participants in a room except the sender
const getOtherParticipants = (roomId, senderId) => {
  if (!rooms.has(roomId)) return [];
  return Array.from(rooms.get(roomId))
    .filter(id => id !== senderId)
    .map(id => {
      const socket = io.sockets.sockets.get(id);
      return {
        id,
        name: socket?.data?.name || 'Unknown Player'
      };
    });
};

// Socket.IO connection handler
io.on('connection', (socket) => {
  console.log(`Video server: User connected: ${socket.id}`);

  // Store user name
  socket.on('register', ({ name }) => {
    socket.data.name = name;
    console.log(`Video server: User ${socket.id} registered as ${name}`);
  });

  // Join a video room
  socket.on('join_room', ({ roomId, name }) => {
    console.log(`Video server: User ${socket.id} (${name}) joining room ${roomId}`);

    // Store user name
    socket.data.name = name;

    // Create room if it doesn't exist
    if (!rooms.has(roomId)) {
      rooms.set(roomId, new Set());
    }

    // Add user to room
    const room = rooms.get(roomId);
    room.add(socket.id);

    // Join socket.io room
    socket.join(roomId);

    // Get other participants
    const otherParticipants = getOtherParticipants(roomId, socket.id);

    // Send list of other participants to the new user
    socket.emit('room_users', { users: otherParticipants });

    // Notify other participants about the new user
    socket.to(roomId).emit('user_joined', {
      id: socket.id,
      name: socket.data.name
    });
  });

  // Leave a video room
  socket.on('leave_room', ({ roomId }) => {
    console.log(`Video server: User ${socket.id} leaving room ${roomId}`);

    if (rooms.has(roomId)) {
      // Remove user from room
      const room = rooms.get(roomId);
      room.delete(socket.id);

      // Delete room if empty
      if (room.size === 0) {
        rooms.delete(roomId);
      }

      // Leave socket.io room
      socket.leave(roomId);

      // Notify other participants
      socket.to(roomId).emit('user_left', { id: socket.id });
    }
  });

  // Handle WebRTC signaling
  socket.on('signal', ({ roomId, to, signal }) => {
    console.log(`Video server: Signal from ${socket.id} to ${to} in room ${roomId}`);

    // Check if the recipient exists
    const recipientSocket = io.sockets.sockets.get(to);
    if (!recipientSocket) {
      console.log(`Video server: Recipient ${to} not found`);
      // Notify sender that recipient is not available
      socket.emit('signal_error', {
        to,
        error: 'Recipient not found'
      });
      return;
    }

    // Check if both are in the same room
    if (!rooms.has(roomId) || !rooms.get(roomId).has(to)) {
      console.log(`Video server: Recipient ${to} not in room ${roomId}`);
      // Notify sender that recipient is not in the room
      socket.emit('signal_error', {
        to,
        error: 'Recipient not in room'
      });
      return;
    }

    // Forward the signal to the recipient
    io.to(to).emit('signal', {
      from: socket.id,
      signal,
      name: socket.data.name
    });
  });

  // Handle disconnection
  socket.on('disconnect', () => {
    console.log(`Video server: User disconnected: ${socket.id}`);

    // Remove user from all rooms
    rooms.forEach((participants, roomId) => {
      if (participants.has(socket.id)) {
        participants.delete(socket.id);

        // Delete room if empty
        if (participants.size === 0) {
          rooms.delete(roomId);
          console.log(`Video server: Room ${roomId} deleted (empty)`);
        } else {
          // Notify other participants in the room
          io.to(roomId).emit('user_left', {
            id: socket.id,
            name: socket.data?.name || 'Unknown Player'
          });
          console.log(`Video server: Notified room ${roomId} that user ${socket.id} left`);
        }
      }
    });
  });
});

// Start server
const PORT = process.env.VIDEO_PORT || 3002;
server.listen(PORT, () => {
  console.log(`Video signaling server running on port ${PORT}`);
});
